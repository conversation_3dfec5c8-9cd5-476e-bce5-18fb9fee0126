#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Solution DÉFINITIVE : Couper physiquement le texte du code-barres
"""

import os
import base64
import barcode
from barcode.writer import ImageWriter
from PIL import Image
import io

def get_logo_base64():
    """Convertit le logo Renault en base64"""
    try:
        logo_paths = ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"]
        for logo_path in logo_paths:
            if os.path.exists(logo_path):
                with open(logo_path, "rb") as f:
                    logo_data = f.read()
                print(f"✅ Logo trouvé: {logo_path}")
                return base64.b64encode(logo_data).decode('utf-8')
        return ""
    except Exception as e:
        print(f"⚠️ Erreur: {e}")
        return ""

def generate_barcode_only_bars_cut(code_value):
    """Génère code-barres et COUPE physiquement le texte"""
    try:
        print(f"✂️ Génération et découpe code-barres pour: {code_value}")
        
        # Générer code-barres avec dimensions exactes 4cm x 2cm
        code128 = barcode.get_barcode_class('code128')
        writer = ImageWriter()

        # Calculer les dimensions en pixels pour 4cm x 2cm à 300 DPI
        target_width_cm = 4.0
        target_height_cm = 2.0
        dpi = 300
        target_width_px = int(target_width_cm * dpi / 2.54)  # 472 pixels
        target_height_px = int(target_height_cm * dpi / 2.54)  # 236 pixels

        writer.set_options({
            'module_width': 0.5,    # Largeur des barres
            'module_height': 30,    # Hauteur des barres
            'quiet_zone': 1,        # Zone de silence minimale
            'dpi': dpi,            # 300 DPI
            'background': 'white',
            'foreground': 'black',
        })
        barcode_instance = code128(code_value, writer=writer)
        
        # Générer l'image complète
        buffer = io.BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)
        
        barcode_img = Image.open(buffer)
        width, height = barcode_img.size

        print(f"📊 Image originale: {width}x{height}")

        # COUPER la partie texte (garder seulement les 70% du haut)
        cut_height = int(height * 0.7)  # Garder 70% du haut
        barcode_only_bars = barcode_img.crop((0, 0, width, cut_height))

        print(f"✂️ Image coupée: {barcode_only_bars.size}")

        # Redimensionner exactement à 4cm x 2cm (472x236 pixels à 300 DPI)
        target_width_px = int(target_width_cm * dpi / 2.54)  # 472 pixels
        target_height_px = int(target_height_cm * dpi / 2.54)  # 236 pixels

        barcode_final = barcode_only_bars.resize((target_width_px, target_height_px), Image.Resampling.LANCZOS)

        print(f"📐 Image finale: {barcode_final.size} (exactement 4cm x 2cm)")
        
        # Convertir en base64
        img_buffer = io.BytesIO()
        barcode_final.save(img_buffer, format='PNG')
        img_buffer.seek(0)

        barcode_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
        return barcode_base64
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return ""

def create_etiquette_barcode_coupe(code_value="64004380-0", part_number="1/2"):
    """Créer étiquette avec code-barres coupé (seulement barres)"""
    
    logo_base64 = get_logo_base64()
    barcode_base64 = generate_barcode_only_bars_cut(code_value)
    
    html_content = f"""<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Étiquette Renault - Code-barres Coupé</title>
  <style>
    body {{
      margin: 0;
      padding: 20px;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
    }}
    .label {{
      width: 6cm;
      height: 3cm;
      background-color: #000;
      border-radius: 8px;
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      font-family: Arial, sans-serif;
      margin: 0 auto;
      box-sizing: border-box;
      overflow: hidden;
      padding: 4px;
    }}
    .logo img {{
      height: 50px;
    }}
    .code-number {{
      font-size: 14px;
      font-weight: bold;
      text-align: center;
    }}
    .barcode-container {{
      /* Zone blanche agrandie : 4cm x 2cm */
      width: 4cm;
      height: 2cm;
      background: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      box-sizing: border-box;
      padding: 0;
      margin: 0;
    }}
    .barcode-img {{
      /* Code-barres aux dimensions exactes - pas d'étirement */
      width: 4cm;
      height: 2cm;
      object-fit: contain;  /* Garde les proportions exactes */
      display: block;
      border-radius: 4px;
    }}
  </style>
</head>
<body>
  <div id="labels"></div>
  <script>
    const label = document.createElement("div");
    label.className = "label";
    label.innerHTML = `
      <div class="logo"><img src="data:image/jpeg;base64,{logo_base64}" alt="Renault"></div>
      <div class="barcode-container">
        <img class="barcode-img" src="data:image/png;base64,{barcode_base64}" alt="Code-barres">
      </div>
      <div class="code-number">{code_value} {part_number}</div>
    `;
    document.getElementById("labels").appendChild(label);
  </script>
</body>
</html>"""
    
    filename = f"etiquette_barcode_coupe_{code_value.replace('-', '_')}_{part_number.replace('/', '_')}.html"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Étiquette avec code-barres coupé créée: {filename}")
    return filename

def main():
    """Test code-barres avec découpe physique du texte"""
    print("✂️ SOLUTION DÉFINITIVE - Code-barres avec découpe du texte")
    print("=" * 60)
    
    # Vérifier qu'un logo existe
    logo_found = any(os.path.exists(path) for path in ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"])
    if not logo_found:
        print("❌ Logo Renault manquant!")
        return
    
    # Créer l'étiquette
    filename = create_etiquette_barcode_coupe("64004380-0", "1/2")
    
    if filename:
        print(f"\n🎉 SOLUTION CRÉÉE:")
        print(f"📁 Fichier: {filename}")
        print("\n💡 Ce qui a été fait:")
        print("✂️ Code-barres généré puis COUPÉ physiquement")
        print("🚫 Partie texte supprimée de l'image")
        print("📊 Seulement les barres restent")
        print("🔢 UN SEUL numéro affiché en bas de l'étiquette")
        
        # Ouvrir le fichier
        try:
            os.system(f"start {filename}")
            print("\n👀 Fichier ouvert - vérifiez le résultat!")
        except:
            print("   (Ouvrez manuellement le fichier)")

if __name__ == "__main__":
    main()
