#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Créer un fichier Excel de test simple
"""

import pandas as pd

# Données de test simples
data = {
    'SKU': ['PRD001', 'PRD002', 'PRD003'],
    'Nom': ['Smartphone', 'Ordinateur', 'Montre'],
    'Description': ['Telephone portable', 'PC de bureau', 'Apple Watch']
}

# Créer DataFrame
df = pd.DataFrame(data)

# Sauvegarder en Excel
df.to_excel('test_simple_data.xlsx', index=False)

print("✅ Fichier test_simple_data.xlsx créé avec succès !")
print(f"📋 Données: {len(df)} lignes, {len(df.columns)} colonnes")
print(df)
