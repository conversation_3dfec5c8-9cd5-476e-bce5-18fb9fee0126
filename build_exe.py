"""
Script pour créer l'exécutable SOMACA
"""
import os
import subprocess
import sys
from pathlib import Path

def install_requirements():
    """Installer les dépendances"""
    print("📦 Installation des dépendances...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
    print("✅ Dépendances installées")

def build_executable():
    """Créer l'exécutable avec PyInstaller"""
    print("🔨 Création de l'exécutable...")
    
    # Commande PyInstaller
    cmd = [
        "pyinstaller",
        "--onefile",                    # Un seul fichier
        "--windowed",                   # Pas de console
        "--name=SOMACA_Generateur",     # Nom de l'exe
        "--icon=web/favicon.ico",       # Icône (optionnel)
        "--add-data=web;web",           # Inclure le dossier web
        "--hidden-import=eel",
        "--hidden-import=pandas",
        "--hidden-import=qrcode",
        "--hidden-import=barcode",
        "--hidden-import=PIL",
        "app.py"
    ]
    
    try:
        subprocess.check_call(cmd)
        print("✅ Exécutable créé avec succès !")
        print("📁 Fichier disponible dans le dossier 'dist/'")
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors de la création: {e}")
        return False
    
    return True

def create_installer_script():
    """Créer un script d'installation simple"""
    installer_content = """
@echo off
echo ========================================
echo    SOMACA - Generateur de Codes-Barres
echo    Installation automatique
echo ========================================
echo.

echo Installation des dependances Python...
pip install -r requirements.txt

echo.
echo Creation de l'executable...
python build_exe.py

echo.
echo ========================================
echo Installation terminee !
echo L'executable se trouve dans le dossier 'dist/'
echo ========================================
pause
"""
    
    with open("installer.bat", "w", encoding="utf-8") as f:
        f.write(installer_content)
    
    print("✅ Script d'installation créé: installer.bat")

def main():
    """Fonction principale"""
    print("🏭 SOMACA - Générateur de Codes-Barres")
    print("🔧 Construction de l'exécutable")
    print("=" * 50)
    
    try:
        # Vérifier que nous sommes dans le bon dossier
        if not os.path.exists("app.py"):
            print("❌ Erreur: app.py non trouvé")
            print("Assurez-vous d'être dans le dossier somaca_web_app")
            return
        
        # Installer les dépendances
        install_requirements()
        
        # Créer l'exécutable
        if build_executable():
            print("\n🎉 Succès !")
            print("📁 Votre application SOMACA.exe est prête dans le dossier 'dist/'")
            print("🚀 Vous pouvez maintenant distribuer ce fichier")
        
        # Créer le script d'installation
        create_installer_script()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        print("💡 Assurez-vous d'avoir Python et pip installés")

if __name__ == "__main__":
    main()
