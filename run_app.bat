@echo off
echo ========================================
echo    SOMACA - Application Modifiée
echo    Lancement avec Python Global
echo ========================================
echo.

echo 🚀 Lancement de l'application avec vos modifications...
echo   ✅ SOMACA agrandi (70pt)
echo   ✅ Position à 0.7cm du haut
echo   ✅ Sous-titre supprimé
echo   ✅ QR code agrandi et centré
echo   ✅ Espace 0.8cm entre SOMACA et QR
echo.

cd /d "%~dp0"
python app_native.py

pause
