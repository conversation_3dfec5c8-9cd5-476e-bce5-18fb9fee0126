#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ÉTIQUETTES FINALES avec dimensions exactes :
- Cadre : 7,0cm x 3,9cm
- Code-barres : 4,5cm x 3cm
- QR code : 2cm x 2cm
"""

import os
import base64
import barcode
from barcode.writer import ImageWriter
import qrcode
from PIL import Image
import io

def get_logo_base64():
    """Convertit le logo Renault en base64"""
    try:
        logo_paths = ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"]
        for logo_path in logo_paths:
            if os.path.exists(logo_path):
                with open(logo_path, "rb") as f:
                    logo_data = f.read()
                print(f"✅ Logo trouvé: {logo_path}")
                return base64.b64encode(logo_data).decode('utf-8')
        return ""
    except Exception as e:
        print(f"⚠️ Erreur: {e}")
        return ""

def generate_barcode_final(code_value):
    """Génère code-barres 4,5cm x 3cm exactement"""
    try:
        print(f"📊 Génération code-barres final pour: {code_value}")
        
        # Dimensions finales : 4,5cm x 3cm
        target_width_cm = 4.5
        target_height_cm = 3.0
        dpi = 300
        target_width_px = int(target_width_cm * dpi / 2.54)  # 531 pixels
        target_height_px = int(target_height_cm * dpi / 2.54)  # 354 pixels
        
        # Générer code-barres
        code128 = barcode.get_barcode_class('code128')
        writer = ImageWriter()
        writer.set_options({
            'module_width': 0.6,    # Barres plus larges
            'module_height': 35,    # Barres plus hautes
            'quiet_zone': 1,        
            'dpi': dpi,            
            'background': 'white',
            'foreground': 'black',
        })
        barcode_instance = code128(code_value, writer=writer)
        
        # Générer l'image
        buffer = io.BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)
        
        barcode_img = Image.open(buffer)
        width, height = barcode_img.size
        
        print(f"📊 Image originale: {width}x{height}")
        
        # COUPER la partie texte (garder seulement les barres)
        cut_height = int(height * 0.7)
        barcode_only_bars = barcode_img.crop((0, 0, width, cut_height))
        
        # Redimensionner exactement à 4,5cm x 3cm
        barcode_final = barcode_only_bars.resize((target_width_px, target_height_px), Image.Resampling.LANCZOS)
        
        print(f"📐 Code-barres final: {barcode_final.size} (4,5cm x 3cm)")
        
        # Convertir en base64
        img_buffer = io.BytesIO()
        barcode_final.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        return base64.b64encode(img_buffer.getvalue()).decode('utf-8')
        
    except Exception as e:
        print(f"❌ Erreur code-barres: {e}")
        return ""

def generate_qr_final(code_value):
    """Génère QR code NATIF 2cm x 2cm (comme il doit être implanté)"""
    try:
        print(f"📱 Génération QR code NATIF pour: {code_value}")

        # Dimensions exactes : 2cm x 2cm à 300 DPI = 236 pixels
        target_size_px = int(2.0 * 300 / 2.54)  # 236 pixels

        # Générer QR code avec box_size calculé pour 2cm exactement
        # Pour un QR code de 236 pixels, on calcule le box_size optimal
        qr = qrcode.QRCode(
            version=1,  # Version 1 = 21x21 modules
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,  # Taille qui donne environ 2cm
            border=1,     # Bordure minimale
        )
        qr.add_data(code_value)
        qr.make(fit=True)

        # Créer l'image QR NATIVE
        qr_img = qr.make_image(fill_color="black", back_color="white")

        print(f"📱 QR natif généré: {qr_img.size}")

        # Ajuster SEULEMENT si nécessaire pour atteindre exactement 236x236
        if qr_img.size[0] != target_size_px:
            # Calculer le nouveau box_size pour obtenir exactement 236 pixels
            current_size = qr_img.size[0]
            ratio = target_size_px / current_size
            new_box_size = max(1, int(10 * ratio))

            print(f"📐 Ajustement: box_size {10} -> {new_box_size}")

            # Régénérer avec le bon box_size
            qr_adjusted = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=new_box_size,
                border=1,
            )
            qr_adjusted.add_data(code_value)
            qr_adjusted.make(fit=True)

            final_img = qr_adjusted.make_image(fill_color="black", back_color="white")
        else:
            final_img = qr_img

        print(f"📐 QR code final: {final_img.size} (2cm x 2cm NATIF)")

        # Convertir en base64
        img_buffer = io.BytesIO()
        final_img.save(img_buffer, format='PNG')
        img_buffer.seek(0)

        return base64.b64encode(img_buffer.getvalue()).decode('utf-8')

    except Exception as e:
        print(f"❌ Erreur QR code: {e}")
        return ""

def create_etiquette_finale(code_value="64004380-0", part_number="1/2", code_type="barcode"):
    """Créer étiquette finale avec dimensions exactes"""
    
    logo_base64 = get_logo_base64()
    
    if code_type == "barcode":
        code_base64 = generate_barcode_final(code_value)
        code_title = "Code-barres"
        container_width = "4.5cm"
        container_height = "3cm"
        code_width = "4.5cm"
        code_height = "3cm"
    else:
        code_base64 = generate_qr_final(code_value)
        code_title = "QR Code"
        container_width = "2.5cm"  # Zone blanche CARRÉE 2,5cm x 2,5cm
        container_height = "2.5cm"  # Zone blanche CARRÉE 2,5cm x 2,5cm
        code_width = "2cm"  # QR code 2cm x 2cm centré (ne dépasse pas)
        code_height = "2cm"
    
    html_content = f"""<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Étiquette Renault Finale - {code_title}</title>
  <style>
    body {{
      margin: 0;
      padding: 20px;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
    }}
    .label {{
      /* Cadre final : 7,0cm x 3,9cm */
      width: 7.0cm;
      height: 3.9cm;
      background-color: #000;
      border-radius: 8px;
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      font-family: Arial, sans-serif;
      margin: 0 auto;
      box-sizing: border-box;
      overflow: hidden;
      padding: 6px;
    }}
    .logo img {{
      height: 55px;  /* Logo plus grand pour le cadre agrandi */
    }}
    .code-number {{
      font-size: 16px;  /* Texte plus grand */
      font-weight: bold;
      text-align: center;
    }}
    .code-container {{
      /* Zone blanche - dimensions selon le type */
      width: {container_width};
      height: {container_height};
      background: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      box-sizing: border-box;
      padding: 0;
      margin: 0;
    }}
    .code-img {{
      /* Code - dimensions exactes */
      width: {code_width};
      height: {code_height};
      object-fit: contain;
      display: block;
      border-radius: 4px;
    }}
  </style>
</head>
<body>
  <div id="labels"></div>
  <script>
    const label = document.createElement("div");
    label.className = "label";
    label.innerHTML = `
      <div class="logo"><img src="data:image/jpeg;base64,{logo_base64}" alt="Renault"></div>
      <div class="code-container">
        <img class="code-img" src="data:image/png;base64,{code_base64}" alt="{code_title}">
      </div>
      <div class="code-number">{code_value} {part_number}</div>
    `;
    document.getElementById("labels").appendChild(label);
  </script>
</body>
</html>"""
    
    filename = f"etiquette_finale_{code_type}_{code_value.replace('-', '_')}_{part_number.replace('/', '_')}.html"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Étiquette finale créée: {filename}")
    return filename

def main():
    """Test étiquettes finales"""
    print("🎯 ÉTIQUETTES FINALES - Dimensions exactes")
    print("=" * 50)
    print("📐 Cadre : 7,0cm x 3,9cm")
    print("📊 Code-barres : 4,5cm x 3cm")
    print("📱 QR code : 2cm x 2cm")
    print("=" * 50)
    
    # Vérifier qu'un logo existe
    logo_found = any(os.path.exists(path) for path in ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"])
    if not logo_found:
        print("❌ Logo Renault manquant!")
        return
    
    # Créer les étiquettes finales
    print("\n📊 Création étiquette code-barres finale...")
    barcode_file = create_etiquette_finale("64004380-0", "1/2", "barcode")
    
    print("\n📱 Création étiquette QR code finale...")
    qr_file = create_etiquette_finale("64004380-0", "1/2", "qr")
    
    if barcode_file and qr_file:
        print(f"\n🎉 ÉTIQUETTES FINALES CRÉÉES:")
        print(f"📁 Code-barres: {barcode_file}")
        print(f"📁 QR code: {qr_file}")
        print("\n💡 Spécifications finales:")
        print("✅ Cadre: 7,0cm x 3,9cm")
        print("✅ Code-barres: 4,5cm x 3cm (seulement barres)")
        print("✅ QR code: 2cm x 2cm")
        print("✅ UN SEUL numéro affiché en bas")
        print("✅ Logo Renault agrandi")
        
        # Ouvrir les fichiers
        try:
            os.system(f"start {barcode_file}")
            os.system(f"start {qr_file}")
        except:
            print("   (Ouvrez manuellement les fichiers)")

if __name__ == "__main__":
    main()
