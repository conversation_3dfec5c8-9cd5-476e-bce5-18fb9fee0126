#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour vérifier que toutes les fonctions Renault sont présentes
"""

import os

def check_renault_functions():
    """Vérifier que toutes les fonctions Renault sont présentes dans les fichiers JS"""
    print("🔍 Vérification des fonctions Renault")
    print("=" * 50)
    
    # Fonctions requises
    required_functions = [
        "generateRenaultEtiquettes",
        "showRenaultChoiceModal", 
        "closeRenaultChoiceModal",
        "generateRenaultWithChoice"
    ]
    
    # Fichiers à vérifier
    js_files = [
        "web/script.js",
        "web/script_native.js"
    ]
    
    for js_file in js_files:
        print(f"\n📄 Vérification de {js_file}:")
        
        if not os.path.exists(js_file):
            print(f"  ❌ Fichier non trouvé")
            continue
            
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        for func in required_functions:
            if f"function {func}(" in content or f"async function {func}(" in content:
                print(f"  ✅ {func}")
            else:
                print(f"  ❌ {func} MANQUANTE")
    
    print(f"\n🎯 Test du bouton HTML:")
    html_file = "web/index.html"
    if os.path.exists(html_file):
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
            
        if 'onclick="generateRenaultEtiquettes()"' in html_content:
            print(f"  ✅ Bouton Renault correctement configuré")
        else:
            print(f"  ❌ Bouton Renault mal configuré")
            
        if 'btn-renault" onclick="generateRenaultEtiquettes()" disabled>' in html_content:
            print(f"  ✅ Bouton initialement désactivé")
        else:
            print(f"  ❌ Bouton pas désactivé au démarrage")
    else:
        print(f"  ❌ Fichier HTML non trouvé")
    
    print(f"\n📋 Résumé:")
    print("- Le bouton Renault devrait maintenant fonctionner")
    print("- Il sera activé après génération de codes")
    print("- Il affichera une modal de choix QR/Barcode")
    print("- Il générera les étiquettes selon le choix")

if __name__ == "__main__":
    check_renault_functions()
