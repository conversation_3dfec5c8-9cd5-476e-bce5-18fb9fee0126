#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Créer un fichier Excel de test pour l'application SOMACA
"""

import pandas as pd
import os

def create_test_file():
    """Créer un fichier Excel de test"""
    data = {
        'Nom': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
        '<PERSON><PERSON><PERSON><PERSON>': ['<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>'],
        'ID': ['001', '002', '003', '004', '005'],
        'Département': ['Production', 'Qualité', 'Logistique', 'RH', 'Maintenance'],
        'Poste': ['Opérateur', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Assistant', 'Technicien']
    }
    
    df = pd.DataFrame(data)
    
    # Sauvegarder le fichier
    filename = 'test_somaca_simple.xlsx'
    df.to_excel(filename, index=False)
    
    print(f"✅ Fichier de test créé: {filename}")
    print(f"📊 {len(df)} lignes, {len(df.columns)} colonnes")
    print(f"📁 Emplacement: {os.path.abspath(filename)}")
    
    return filename

if __name__ == '__main__':
    create_test_file()
