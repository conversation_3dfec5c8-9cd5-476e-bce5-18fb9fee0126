<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Test QR Simple</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
    }
    .label {
      /* Cadre : 7,0cm x 3,9cm */
      width: 7.0cm;
      height: 3.9cm;
      background-color: #000;
      border-radius: 8px;
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-family: Arial, sans-serif;
      margin: 0 auto;
      box-sizing: border-box;
      overflow: hidden;
      padding: 6px;
    }
    .qr-container {
      /* Zone blanche CARRÉE : 2,5cm x 2,5cm */
      width: 2.5cm;
      height: 2.5cm;
      background: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      box-sizing: border-box;
      padding: 0;
      margin: 0;
      /* Bordure pour voir la zone */
      border: 2px solid #ff0000;
    }
    .qr-img {
      /* QR code : 2cm x 2cm */
      width: 2cm;
      height: 2cm;
      object-fit: contain;
      display: block;
      border-radius: 2px;
    }
  </style>
</head>
<body>
  <div id="labels"></div>
  <script>
    const label = document.createElement("div");
    label.className = "label";
    label.innerHTML = `
      <div class="qr-container">
        <img class="qr-img" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOYAAADmAQAAAADpEcQWAAABV0lEQVR4nO1Zy2oDMQyU7ECP3j9yPt37QQXnGFijMpK3Cdt0eypWqedg7NUhg9BjpLDQ92jhxEj036wk5fMmNQpR2vYPafPJOYyzJgE2vBpTrvAaUBxzHmZtzLwQ0bpEoVVvzPz7v/uXrbnuhydWfqyX50e6s6zL+3hWwa2vInpho/2G4BLPnMM4642BC+pV0ypF+oGvjjkP0gwPFI0ra4uKqRle6KuHyKKuGTY9fHIOg6wkYt0vmgw1pbU/fXIOQ3OQsnoI0VSQgwVeK9NXB3S/7C2wp1+Ns159hU2BJkOlh5Rl44yrn/pgRkjhNuPqiKCzn4J0drYj3RmToVfOHvYMEbMzKtftTfPSLefhewZCqcq1MV/TRvjml/O4PkgmrapVKVMPOHxyDg58VewpXT3MPvjCimjSrRXchBxU/83Z+WzPQGiBuhfVoXDukA/g+T/OE87r1QeLMOxr8GpjFAAAAABJRU5ErkJggg==" alt="QR Code">
      </div>
    `;
    document.getElementById("labels").appendChild(label);
  </script>
</body>
</html>