// Variables globales
let isGenerating = false;
let currentOperation = null;

// Fonctions utilitaires
function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    const messageElement = document.getElementById('notification-message');
    
    messageElement.textContent = message;
    notification.className = `notification show ${type}`;
    
    // Auto-fermeture après 5 secondes
    setTimeout(() => {
        closeNotification();
    }, 5000);
}

function closeNotification() {
    const notification = document.getElementById('notification');
    notification.classList.remove('show');
}

function updateProgress(percentage) {
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');
    
    progressFill.style.width = percentage + '%';
    progressText.textContent = percentage + '%';
}

function updateStatus(message) {
    const statusMessage = document.getElementById('status-message');
    statusMessage.textContent = message;
}

// Fonction appelée depuis Python pour mettre à jour le progrès EN TEMPS RÉEL
function update_progress(percentage) {
    updateProgress(percentage);
    updateStatus(`Génération en cours... ${percentage}%`);
}

// Fonction appelée depuis Python quand la génération est terminée
function generation_complete(result) {
    isGenerating = false;

    // Masquer le bouton d'annulation
    hideCancelButton();

    // Réactiver le bouton de génération
    const generateBtn = document.querySelector('.btn-generate');
    generateBtn.disabled = false;
    generateBtn.innerHTML = '<i class="fas fa-rocket"></i> Générer les Codes';

    if (result.success) {
        showNotification(result.message, 'success');
        updateStatus('Génération terminée !');
        updateProgress(100);

        // Forcer la réactivation du bouton d'impression
        forceEnablePrintButton();
    } else {
        showNotification(result.message, 'error');
        updateStatus('Erreur lors de la génération');
        updateProgress(0);

        // Même en cas d'erreur, réactiver le bouton
        forceEnablePrintButton();
    }
}

// Fonction pour réinitialiser l'application
async function resetApplication() {
    try {
        console.log('🔄 Réinitialisation de l\'application...');
        const result = await pywebview.api.reset_app();

        if (result.success) {
            console.log('✅ Application réinitialisée');
            // Réinitialiser l'interface
            updateProgress(0);
            updateStatus('Prêt');
            isGenerating = false;
            hideCancelButton();
        } else {
            console.error('❌ Erreur réinitialisation:', result.message);
        }
    } catch (error) {
        console.error('❌ Erreur lors de la réinitialisation:', error);
    }
}

// Sélection de fichier avec pywebview
async function selectFile() {
    try {
        updateStatus('Sélection du fichier...');
        const result = await pywebview.api.select_file();

        if (result.success) {
            // Extraire le chemin du fichier du message
            let filePath = result.message;
            if (filePath.startsWith('Fichier sélectionné: ')) {
                filePath = filePath.replace('Fichier sélectionné: ', '');
            }

            console.log('Chemin du fichier:', filePath);
            document.getElementById('source-file').value = filePath;
            showNotification('Fichier sélectionné avec succès', 'success');
            updateStatus('Fichier Excel sélectionné');

            // Réinitialiser le bouton d'export (désactiver)
            resetExportButton();
        } else {
            showNotification(result.message, 'error');
            updateStatus('Erreur lors de la sélection du fichier');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showNotification('Erreur lors de la sélection du fichier', 'error');
        updateStatus('Erreur');
    }
}

// Sélection de dossier avec pywebview
async function selectFolder() {
    try {
        updateStatus('Sélection du dossier...');
        const result = await pywebview.api.select_folder();

        if (result.success) {
            // Extraire le chemin du dossier du message
            let folderPath = result.message;
            if (folderPath.startsWith('Dossier sélectionné: ')) {
                folderPath = folderPath.replace('Dossier sélectionné: ', '');
            }

            console.log('Chemin du dossier:', folderPath);
            document.getElementById('dest-folder').value = folderPath;
            showNotification('Dossier sélectionné avec succès', 'success');
            updateStatus('Dossier de destination sélectionné');
        } else {
            showNotification(result.message, 'error');
            updateStatus('Erreur lors de la sélection du dossier');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showNotification('Erreur lors de la sélection du dossier', 'error');
        updateStatus('Erreur');
    }
}

// Méthode de sélection de fichier
async function selectFileMethod() {
    await selectFile();
}

// Gestion de la sélection de fichier HTML (fallback)
function handleFileSelect(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];
        const filePath = file.path || file.webkitRelativePath || file.name;

        // Vérifier l'extension
        if (filePath.endsWith('.xlsx') || filePath.endsWith('.xls')) {
            document.getElementById('source-file').value = filePath;
            showNotification('Fichier sélectionné', 'success');
            updateStatus('Fichier Excel sélectionné');
            resetExportButton();
        } else {
            showNotification('Veuillez sélectionner un fichier Excel (.xlsx ou .xls)', 'error');
        }
    }
}

function resetExportButton() {
    const printBtn = document.getElementById('btn-print');
    printBtn.disabled = true;
    printBtn.innerHTML = '<i class="fas fa-print"></i> Options d\'Impression & Étiquettes';
}

// Gestionnaire de clic pour le bouton d'impression
function handlePrintClick(e) {
    console.log('=== handlePrintClick appelé ===');
    console.log('Event:', e);
    const printBtn = document.getElementById('btn-print');
    if (printBtn && !printBtn.disabled && !isGenerating) {
        console.log('Conditions OK - Appel showPrintOptions');
        showPrintOptions();
    } else {
        console.log('Conditions NOK - disabled:', printBtn?.disabled, 'isGenerating:', isGenerating);
    }
}

// Fonction pour forcer la réactivation du bouton d'impression
function forceEnablePrintButton() {
    console.log('forceEnablePrintButton appelé');
    const printBtn = document.getElementById('btn-print');
    if (printBtn) {
        // Forcer la réactivation de manière agressive SANS toucher au innerHTML
        printBtn.disabled = false;
        printBtn.removeAttribute('disabled');
        printBtn.style.pointerEvents = 'auto';
        printBtn.style.opacity = '1';

        // NE PAS modifier innerHTML pour préserver l'événement onclick original
        // printBtn.innerHTML = '<i class="fas fa-print"></i> Options d\'Impression & Étiquettes';

        // FORCER le réattachement de l'événement onclick
        console.log('FORCER réattachement onclick');
        printBtn.onclick = function(e) {
            console.log('=== CLIC DÉTECTÉ SUR BOUTON IMPRESSION ===');
            console.log('État du bouton:', printBtn.disabled);
            console.log('isGenerating:', isGenerating);
            console.log('currentOperation:', currentOperation);

            if (!printBtn.disabled && !isGenerating) {
                console.log('Conditions OK - Appel de showPrintOptions()');
                showPrintOptions();
            } else {
                console.log('Conditions NOK - disabled:', printBtn.disabled, 'isGenerating:', isGenerating);
                // Forcer la réinitialisation si les variables sont incohérentes
                if (printBtn.disabled === false && isGenerating === true && currentOperation === null) {
                    console.log('FORCER réinitialisation des variables incohérentes');
                    isGenerating = false;
                    currentOperation = null;
                    showPrintOptions();
                }
            }
        };

        // Ajouter aussi un addEventListener au cas où
        printBtn.removeEventListener('click', handlePrintClick); // Supprimer d'abord pour éviter les doublons
        printBtn.addEventListener('click', handlePrintClick);

        console.log('Bouton d\'impression forcé à enabled avec événements vérifiés');
    } else {
        console.error('Bouton d\'impression non trouvé !');
    }

    // Réinitialiser les variables globales
    isGenerating = false;
    currentOperation = null;
    console.log('Variables globales réinitialisées - isGenerating:', isGenerating, 'currentOperation:', currentOperation);
}

// Fonction pour vérifier l'état du bouton d'impression
function checkButtonState() {
    const printBtn = document.getElementById('btn-print');
    console.log('=== ÉTAT DU BOUTON D\'IMPRESSION ===');
    console.log('Bouton trouvé:', !!printBtn);
    if (printBtn) {
        console.log('disabled:', printBtn.disabled);
        console.log('innerHTML:', printBtn.innerHTML);
        console.log('style.pointerEvents:', printBtn.style.pointerEvents);
        console.log('style.opacity:', printBtn.style.opacity);
        console.log('onclick:', !!printBtn.onclick);
    }
    console.log('isGenerating:', isGenerating);
    console.log('currentOperation:', currentOperation);
    console.log('=====================================');

    // Afficher aussi dans une notification
    const status = printBtn ? (printBtn.disabled ? 'DÉSACTIVÉ' : 'ACTIVÉ') : 'NON TROUVÉ';
    showNotification(`Bouton: ${status} | isGenerating: ${isGenerating} | currentOperation: ${currentOperation}`, 'info');
}

function showCancelButton() {
    const cancelBtn = document.getElementById('btn-cancel');
    cancelBtn.style.display = 'inline-block';
    cancelBtn.disabled = false;
}

function hideCancelButton() {
    const cancelBtn = document.getElementById('btn-cancel');
    cancelBtn.style.display = 'none';
    cancelBtn.disabled = true;
}

// Démarrer la génération
async function startGeneration() {
    if (isGenerating) {
        showNotification('Génération déjà en cours...', 'warning');
        return;
    }

    // Vérifier les champs requis
    const sourceFile = document.getElementById('source-file').value;
    const destFolder = document.getElementById('dest-folder').value;

    if (!sourceFile) {
        showNotification('Veuillez sélectionner un fichier Excel source', 'error');
        return;
    }

    if (!destFolder) {
        showNotification('Veuillez sélectionner un dossier de destination', 'error');
        return;
    }

    // Générer toujours les deux types de codes
    const generateBarcodes = true;
    const generateQR = true;

    // Debug info
    try {
        const debugInfo = await pywebview.api.debug_info();
        console.log('Debug info:', debugInfo);

        // Afficher les informations de debug à l'utilisateur
        if (!debugInfo.input_exists) {
            showNotification(`Fichier non trouvé: ${debugInfo.input_file}`, 'error');
            return;
        }
        if (!debugInfo.output_exists) {
            showNotification(`Dossier non trouvé: ${debugInfo.output_folder}`, 'error');
            return;
        }
    } catch (e) {
        console.log('Erreur debug:', e);
    }
    
    try {
        isGenerating = true;
        currentOperation = 'generation';
        updateStatus('Génération en cours...');
        updateProgress(0);

        // Désactiver le bouton et afficher le bouton d'annulation
        const generateBtn = document.querySelector('.btn-generate');
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Génération en cours...';

        // Afficher le bouton d'annulation
        showCancelButton();

        showNotification('Démarrage de la génération...', 'info');
        
        // Démarrer la génération (synchrone avec webview)
        const result = await pywebview.api.start_generation(generateBarcodes, generateQR);

        // Vérifier si il y a un conflit de fichier
        if (result.file_exists) {
            // Afficher la modal de conflit de fichier
            showFileExistsDialog(result.filename, generateBarcodes, generateQR);
        } else {
            // La génération est terminée, traiter le résultat
            generation_complete(result);
        }
        
    } catch (error) {
        console.error('Erreur:', error);
        showNotification('Erreur lors du démarrage de la génération', 'error');
        updateStatus('Erreur');
        
        // Réinitialiser l'interface
        isGenerating = false;
        hideCancelButton();
        const generateBtn = document.querySelector('.btn-generate');
        generateBtn.disabled = false;
        generateBtn.innerHTML = '<i class="fas fa-rocket"></i> Générer les Codes';
    }
}

// Afficher les options d'impression
function showPrintOptions() {
    console.log('=== showPrintOptions appelé ===');
    console.log('État actuel - isGenerating:', isGenerating, 'currentOperation:', currentOperation);

    // Vérifier l'état du bouton
    const printBtn = document.getElementById('btn-print');
    if (printBtn) {
        console.log('État du bouton - disabled:', printBtn.disabled, 'style.pointerEvents:', printBtn.style.pointerEvents);
    }

    // FORCER la réinitialisation des variables au cas où elles seraient bloquées
    if (isGenerating && currentOperation === null) {
        console.log('Variables incohérentes détectées - Réinitialisation forcée');
        isGenerating = false;
    }

    if (isGenerating) {
        console.log('Génération en cours, affichage d\'un avertissement');
        showNotification('Veuillez attendre la fin de la génération', 'warning');
        return;
    }

    // Les deux types de codes sont toujours disponibles
    const generateBarcodes = true;
    const generateQR = true;

    console.log('Ouverture de la modal d\'options d\'impression');

    // S'assurer que le DOM est prêt
    if (document.readyState === 'loading') {
        console.log('DOM en cours de chargement, attente...');
        document.addEventListener('DOMContentLoaded', showPrintOptions);
        return;
    }

    // Debug: Lister toutes les modals disponibles
    const allModals = document.querySelectorAll('[id*="modal"]');
    console.log('Modals disponibles:', Array.from(allModals).map(m => m.id));

    // Debug: Vérifier le DOM
    console.log('Document ready state:', document.readyState);
    console.log('Body children count:', document.body.children.length);

    // Afficher la modal d'options d'impression
    let modal = document.getElementById('print-options-modal');

    if (!modal) {
        console.error('ERREUR: Modal print-options-modal non trouvée !');
        console.log('Tentative de recherche par classe...');
        const modalByClass = document.querySelector('.modal-overlay');
        if (modalByClass && modalByClass.id === 'print-options-modal') {
            console.log('Modal trouvée par classe:', modalByClass.id);
            modal = modalByClass;
        } else {
            console.log('Création forcée de la modal...');
            modal = createPrintOptionsModal();
            if (!modal) {
                alert('Erreur: Impossible de créer la modal');
                return;
            }
        }
    }

    console.log('Modal trouvée, affichage...');
    modal.style.display = 'flex';
    console.log('Modal affichée - style.display:', modal.style.display);
}

// Fonction pour créer la modal dynamiquement si elle n'existe pas
function createPrintOptionsModal() {
    console.log('Création dynamique de la modal print-options...');

    const modalHTML = `
        <div class="modal-overlay" id="print-options-modal" style="display: none;">
            <div class="modal-dialog modal-elegant">
                <div class="modal-header-elegant">
                    <div class="modal-icon-elegant">
                        <i class="fas fa-print"></i>
                    </div>
                    <h3>Options d'Impression</h3>
                    <p class="modal-subtitle-elegant">Choisissez votre méthode</p>
                </div>
                <div class="modal-buttons-elegant">
                    <button class="btn-elegant btn-renault-elegant" onclick="handlePrintChoice('renault')">
                        <div class="btn-icon-elegant">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="btn-text-elegant">
                            <span class="btn-title-elegant">Étiquettes Renault</span>
                            <span class="btn-desc-elegant">Tickets 4x7cm</span>
                        </div>
                    </button>
                    <button class="btn-elegant btn-export-elegant" onclick="handlePrintChoice('export')">
                        <div class="btn-icon-elegant">
                            <i class="fas fa-download"></i>
                        </div>
                        <div class="btn-text-elegant">
                            <span class="btn-title-elegant">Exporter</span>
                            <span class="btn-desc-elegant">Sauvegarder images</span>
                        </div>
                    </button>
                    <button class="btn-elegant btn-cancel-elegant" onclick="handlePrintChoice('cancel')">
                        <div class="btn-icon-elegant">
                            <i class="fas fa-times"></i>
                        </div>
                        <div class="btn-text-elegant">
                            <span class="btn-title-elegant">Annuler</span>
                            <span class="btn-desc-elegant">Fermer</span>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    `;

    // Ajouter la modal au body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Retourner l'élément créé
    const modal = document.getElementById('print-options-modal');
    console.log('Modal créée dynamiquement:', modal ? 'Succès' : 'Échec');
    return modal;
}

// Gérer le choix d'impression
async function handlePrintChoice(choice) {
    console.log('handlePrintChoice appelé avec choice:', choice);

    const modal = document.getElementById('print-options-modal');
    modal.style.display = 'none';

    if (choice === 'cancel') {
        console.log('Choix annulé');
        return;
    }

    // Toujours générer les deux types de codes
    const generateBarcodes = true;
    const generateQR = true;

    try {
        if (choice === 'export') {
            // Export dans un dossier (fonctionnalité existante)
            await exportToFolder(generateBarcodes, generateQR);
        } else if (choice === 'renault') {
            // Étiquettes Renault (ancienne fonction generateRenaultEtiquettes)
            console.log('DÉBUT génération Renault - Désactivation du bouton');
            const printBtn = document.getElementById('btn-print');
            printBtn.disabled = true;

            await generateRenaultEtiquettes();

            // FORCER la réactivation immédiatement après
            console.log('FIN génération Renault - FORCER réactivation du bouton');
            setTimeout(() => {
                forceEnablePrintButton();
                console.log('Bouton forcé à enabled après timeout');
            }, 1000);
        }
    } catch (error) {
        console.error('Erreur choix impression:', error);
        showNotification('Erreur lors du traitement', 'error');
        // Réactiver le bouton même en cas d'erreur
        forceEnablePrintButton();
    } finally {
        // Réactivation de sécurité
        setTimeout(() => {
            console.log('Finally - Réactivation de sécurité du bouton');
            forceEnablePrintButton();
        }, 2000);
    }
}

// Fonction exportImages pour la modal d'export
async function exportImages() {
    // Fermer la modal d'export
    const modal = document.getElementById('export-options-modal');
    modal.style.display = 'none';

    // Récupérer les options de la modal d'export
    const generateBarcodes = document.getElementById('export-barcodes').checked;
    const generateQR = document.getElementById('export-qr').checked;

    if (!generateBarcodes && !generateQR) {
        showNotification('Veuillez sélectionner au moins un type de code à exporter', 'warning');
        return;
    }

    // Appeler la fonction d'export
    await exportToFolder(generateBarcodes, generateQR);
}

// Export dans un dossier (ancienne fonction exportImages)
async function exportToFolder(generateBarcodes, generateQR) {
    try {
        currentOperation = 'export';
        updateStatus('Export des images en cours...');
        showNotification('Démarrage de l\'export des images...', 'info');

        // Désactiver le bouton pendant l'export
        const printBtn = document.getElementById('btn-print');
        printBtn.disabled = true;
        printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Export en cours...';

        // Afficher le bouton d'annulation
        showCancelButton();

        // Passer les options au backend
        const result = await pywebview.api.export_images_for_print(generateBarcodes, generateQR);

        if (result.success) {
            showNotification(result.message, 'success');
            updateStatus('Export des images terminé !');
        } else {
            showNotification(result.message, 'error');
            updateStatus('Erreur lors de l\'export');
        }

    } catch (error) {
        console.error('Erreur export:', error);
        showNotification('Erreur lors de l\'export des images', 'error');
        updateStatus('Erreur lors de l\'export');
    } finally {
        // Masquer le bouton d'annulation
        hideCancelButton();

        // Forcer la réactivation du bouton
        forceEnablePrintButton();
    }
}

// Fonction printDirectly pour la modal d'impression
async function printDirectly() {
    // Fermer la modal d'impression directe
    const modal = document.getElementById('direct-print-modal');
    modal.style.display = 'none';

    // Récupérer les options de la modal d'impression
    const generateBarcodes = document.getElementById('print-barcodes').checked;
    const generateQR = document.getElementById('print-qr').checked;

    if (!generateBarcodes && !generateQR) {
        showNotification('Veuillez sélectionner au moins un type de code à imprimer', 'warning');
        return;
    }

    // Appeler la fonction d'impression
    await printDirectlyWithOptions(generateBarcodes, generateQR);
}

// Impression directe (nouvelle fonctionnalité)
async function printDirectlyWithOptions(generateBarcodes, generateQR) {
    try {
        currentOperation = 'print';
        updateStatus('Préparation de l\'impression...');
        showNotification('Détection de l\'imprimante et préparation...', 'info');

        // Désactiver le bouton pendant l'impression
        const printBtn = document.getElementById('btn-print');
        printBtn.disabled = true;
        printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Impression en cours...';

        // Afficher le bouton d'annulation
        showCancelButton();

        // Appeler la nouvelle fonction d'impression directe
        const result = await pywebview.api.print_directly(generateBarcodes, generateQR);

        if (result.success) {
            showNotification(result.message, 'success');
            updateStatus('Impression terminée !');
        } else {
            showNotification(result.message, 'error');
            updateStatus('Erreur lors de l\'impression');
        }

    } catch (error) {
        console.error('Erreur impression:', error);
        showNotification('Erreur lors de l\'impression directe', 'error');
        updateStatus('Erreur lors de l\'impression');
    } finally {
        // Masquer le bouton d'annulation
        hideCancelButton();

        // Forcer la réactivation du bouton
        forceEnablePrintButton();
    }
}

// Annuler l'opération en cours
async function cancelOperation() {
    try {
        showNotification('Annulation en cours...', 'info');
        const result = await pywebview.api.cancel_current_operation();
        
        if (result.success) {
            showNotification('Opération annulée', 'info');
            updateStatus('Opération annulée');
        } else {
            showNotification(result.message, 'error');
        }
        
        // Réinitialiser l'interface
        isGenerating = false;
        hideCancelButton();
        
        const generateBtn = document.querySelector('.btn-generate');
        generateBtn.disabled = false;
        generateBtn.innerHTML = '<i class="fas fa-rocket"></i> Générer les Codes';
        
        const printBtn = document.getElementById('btn-print');
        printBtn.disabled = false;
        printBtn.innerHTML = '<i class="fas fa-print"></i> Options d\'Impression & Étiquettes';
        
    } catch (error) {
        console.error('Erreur annulation:', error);
        showNotification('Erreur lors de l\'annulation', 'error');
    }
}

// Fonction pour afficher le dialogue de fichier existant
function showFileExistsDialog(filename, generateBarcodes, generateQR) {
    const modal = document.getElementById('file-exists-modal');
    const filenameElement = document.getElementById('existing-filename');

    filenameElement.textContent = filename;
    modal.style.display = 'flex';

    // Stocker les paramètres pour la génération
    window.pendingGeneration = {
        filename: filename,
        generateBarcodes: generateBarcodes,
        generateQR: generateQR
    };
}

// Gérer le choix de l'utilisateur pour le fichier existant
async function handleFileChoice(choice) {
    const modal = document.getElementById('file-exists-modal');
    modal.style.display = 'none';

    try {
        if (choice === 'cancel') {
            showNotification('Génération annulée', 'info');
            updateStatus('Génération annulée par l\'utilisateur');

            // Réactiver les boutons
            const generateBtn = document.querySelector('.btn-generate');
            generateBtn.disabled = false;
            generateBtn.innerHTML = '<i class="fas fa-rocket"></i> Générer les Codes';
            isGenerating = false;
            hideCancelButton();
            return;
        }

        // Continuer la génération avec le choix utilisateur
        if (window.pendingGeneration) {
            const { filename, generateBarcodes, generateQR } = window.pendingGeneration;

            showNotification(`Génération en cours avec l'option: ${choice}`, 'info');

            const result = await pywebview.api.generate_codes_with_user_choice(
                generateBarcodes,
                generateQR,
                choice,
                filename
            );

            // Traiter le résultat final
            generation_complete(result);

            // Nettoyer
            delete window.pendingGeneration;
        }

    } catch (error) {
        console.error('Erreur choix fichier:', error);
        showNotification('Erreur lors du traitement du choix', 'error');

        // Réinitialiser l'interface en cas d'erreur
        isGenerating = false;
        hideCancelButton();
        const generateBtn = document.querySelector('.btn-generate');
        generateBtn.disabled = false;
        generateBtn.innerHTML = '<i class="fas fa-rocket"></i> Générer les Codes';
    }
}



// Fonction pour générer les étiquettes Renault
async function generateRenaultEtiquettes() {
    try {
        currentOperation = 'renault';
        updateStatus('Vérification des codes existants dans le fichier Excel généré...');

        // Appeler la fonction Python pour vérifier
        const result = await pywebview.api.generate_renault_etiquettes();

        if (result.success && result.action === 'show_choice_modal') {
            // Afficher la modal de choix
            showRenaultChoiceModal();
            // Réactiver le bouton immédiatement après affichage de la modal
            console.log('Modal affichée - Réactivation du bouton d\'impression');
            forceEnablePrintButton();
        } else if (result.success) {
            updateProgress(100);
            updateStatus(`Étiquettes Renault générées avec succès !`);
            showNotification(result.message, 'success');

            // Simuler generation_complete pour réactiver le bouton
            console.log('Succès direct - Simulation de generation_complete');
            generation_complete({success: true, message: result.message});

            // Ouvrir le dossier automatiquement (sans confirmation)
            try {
                await pywebview.api.open_folder_explorer(result.folder);
                console.log('Dossier ouvert automatiquement');
            } catch (e) {
                console.log('Ouverture dossier non supportée');
            }
        } else {
            updateStatus('Erreur lors de la génération des étiquettes Renault');
            showNotification(result.message, 'error');
            // Réactiver le bouton même en cas d'erreur
            console.log('Erreur - Réactivation du bouton d\'impression');
            forceEnablePrintButton();
        }

    } catch (error) {
        console.error('Erreur:', error);
        updateStatus('Erreur lors de la génération des étiquettes Renault');
        showNotification('Erreur lors de la génération des étiquettes Renault', 'error');
    } finally {
        // Forcer la réactivation du bouton
        console.log('Finally block - Réactivation du bouton d\'impression');
        forceEnablePrintButton();

        // Timer de sécurité pour forcer la réactivation après 2 secondes
        setTimeout(() => {
            console.log('Timer de sécurité - Réactivation forcée du bouton d\'impression');
            forceEnablePrintButton();
        }, 2000);
    }
}

// Fonction pour afficher la modal de choix Renault
function showRenaultChoiceModal() {
    const modalHtml = `
        <div class="modal-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.7); display: flex; justify-content: center; align-items: center; z-index: 1000; backdrop-filter: blur(3px);">
            <div class="modal-content" style="background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); padding: 40px; border-radius: 15px; max-width: 550px; width: 90%; box-shadow: 0 20px 40px rgba(0,0,0,0.15); border: 1px solid #e9ecef; animation: modalSlideIn 0.3s ease-out;">

                <!-- En-tête avec icône -->
                <div style="text-align: center; margin-bottom: 30px;">
                    <div style="background: linear-gradient(135deg, #007bff, #0056b3); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 16px rgba(0,123,255,0.3);">
                        <i class="fas fa-palette" style="font-size: 35px; color: white;"></i>
                    </div>
                    <h3 style="margin: 0; color: #2c3e50; font-size: 24px; font-weight: 600;">Tickets Renault</h3>
                    <p style="color: #6c757d; margin: 8px 0 0 0; font-size: 16px;">Choisissez la couleur de fond pour vos tickets</p>
                </div>

                <!-- Options de couleur -->
                <div style="margin: 30px 0;">
                    <label class="color-option" style="display: flex; align-items: center; margin-bottom: 20px; cursor: pointer; padding: 15px; border: 2px solid #e9ecef; border-radius: 12px; transition: all 0.3s ease; background: white;" onmouseover="this.style.borderColor='#007bff'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 16px rgba(0,123,255,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                        <input type="checkbox" id="background-black" checked style="margin-right: 15px; transform: scale(1.3);">
                        <div style="display: flex; align-items: center; flex: 1;">
                            <div style="background: #000; color: white; padding: 12px 20px; border-radius: 8px; font-size: 16px; font-weight: 600; margin-right: 15px; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
                                ⚫ Fond Noir
                            </div>
                            <div style="color: #6c757d; font-size: 14px;">
                                Logo blanc sur fond noir<br>
                                <small style="color: #adb5bd;">Style classique et élégant</small>
                            </div>
                        </div>
                    </label>

                    <label class="color-option" style="display: flex; align-items: center; margin-bottom: 20px; cursor: pointer; padding: 15px; border: 2px solid #e9ecef; border-radius: 12px; transition: all 0.3s ease; background: white;" onmouseover="this.style.borderColor='#007bff'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 16px rgba(0,123,255,0.1)'" onmouseout="this.style.borderColor='#e9ecef'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                        <input type="checkbox" id="background-white" style="margin-right: 15px; transform: scale(1.3);">
                        <div style="display: flex; align-items: center; flex: 1;">
                            <div style="background: #fff; color: #333; padding: 12px 20px; border-radius: 8px; font-size: 16px; font-weight: 600; margin-right: 15px; border: 2px solid #dee2e6; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                                ⚪ Fond Blanc
                            </div>
                            <div style="color: #6c757d; font-size: 14px;">
                                Logo noir sur fond blanc<br>
                                <small style="color: #adb5bd;">Style moderne et contrasté</small>
                            </div>
                        </div>
                    </label>
                </div>

                <!-- Info box -->
                <div style="background: linear-gradient(135deg, #e3f2fd, #f3e5f5); padding: 20px; border-radius: 10px; margin: 25px 0; border-left: 4px solid #2196f3;">
                    <div style="display: flex; align-items: center;">
                        <i class="fas fa-lightbulb" style="color: #2196f3; font-size: 20px; margin-right: 12px;"></i>
                        <div>
                            <p style="margin: 0; font-size: 14px; color: #37474f; font-weight: 500;">
                                <strong>Astuce :</strong> Sélectionnez les deux options pour générer automatiquement les tickets dans les deux couleurs.
                            </p>
                            <p style="margin: 5px 0 0 0; font-size: 12px; color: #607d8b;">
                                Chaque couleur sera sauvegardée dans un dossier séparé.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Boutons -->
                <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px;">
                    <button onclick="closeRenaultChoiceModal()" style="padding: 12px 25px; border: 2px solid #6c757d; background: transparent; color: #6c757d; border-radius: 8px; cursor: pointer; font-weight: 500; transition: all 0.3s ease;" onmouseover="this.style.background='#6c757d'; this.style.color='white'" onmouseout="this.style.background='transparent'; this.style.color='#6c757d'">
                        <i class="fas fa-times" style="margin-right: 8px;"></i>Annuler
                    </button>
                    <button onclick="generateRenaultWithChoice()" style="padding: 12px 25px; border: none; background: linear-gradient(135deg, #28a745, #20c997); color: white; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(40,167,69,0.3);" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 16px rgba(40,167,69,0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(40,167,69,0.3)'">
                        <i class="fas fa-rocket" style="margin-right: 8px;"></i>Générer les Tickets
                    </button>
                </div>
            </div>
        </div>

        <style>
            @keyframes modalSlideIn {
                from {
                    opacity: 0;
                    transform: translateY(-50px) scale(0.9);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }
        </style>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

// Fonction pour fermer la modal de choix
function closeRenaultChoiceModal() {
    console.log('Fermeture de la modal Renault');

    // Supprimer toutes les modals immédiatement
    const modals = document.querySelectorAll('.modal-overlay');
    modals.forEach(modal => {
        console.log('Suppression modal trouvée');
        modal.remove();
    });

    // Double vérification - supprimer par classe aussi
    const modalsByClass = document.getElementsByClassName('modal-overlay');
    while (modalsByClass.length > 0) {
        modalsByClass[0].remove();
    }

    console.log('Modal fermée');
}

// Fonction pour générer avec le choix utilisateur
function generateRenaultWithChoice() {
    console.log('Fonction generateRenaultWithChoice appelée');

    // RÉCUPÉRER LES VALEURS AVANT DE FERMER LA MODAL
    const blackCheckbox = document.getElementById('background-black');
    const whiteCheckbox = document.getElementById('background-white');

    console.log('Checkbox noir trouvé:', blackCheckbox);
    console.log('Checkbox blanc trouvé:', whiteCheckbox);

    const blackSelected = blackCheckbox ? blackCheckbox.checked : false;
    const whiteSelected = whiteCheckbox ? whiteCheckbox.checked : false;

    console.log('Noir sélectionné:', blackSelected);
    console.log('Blanc sélectionné:', whiteSelected);

    if (!blackSelected && !whiteSelected) {
        console.log('Aucune couleur sélectionnée - affichage erreur');
        showNotification('Veuillez sélectionner au moins une couleur de fond', 'error');
        return;
    }

    // Préparer les couleurs sélectionnées
    const selectedColors = [];
    if (blackSelected) selectedColors.push('black');
    if (whiteSelected) selectedColors.push('white');

    console.log('Couleurs sélectionnées:', selectedColors);

    // FERMER LA MODAL IMMÉDIATEMENT - MÉTHODE FORCÉE
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        console.log('Fermeture forcée de la modal');
        modal.remove();
    }

    // Supprimer toutes les modals au cas où
    const allModals = document.querySelectorAll('.modal-overlay');
    allModals.forEach(m => m.remove());

    // Démarrer la génération immédiatement
    startRenaultGeneration(selectedColors);
}

// Fonction séparée pour la génération
async function startRenaultGeneration(selectedColors) {
    try {
        // Afficher le statut initial et la progression
        updateStatus(`🚀 Démarrage de la génération des tickets (${selectedColors.length} couleur${selectedColors.length > 1 ? 's' : ''})...`);
        updateProgress(5);

        // Simuler progression initiale
        await new Promise(resolve => setTimeout(resolve, 300));
        updateProgress(10);
        updateStatus('📊 Lecture du fichier Excel...');

        await new Promise(resolve => setTimeout(resolve, 200));
        updateProgress(20);
        updateStatus('🎨 Préparation des couleurs et logos...');

        // Appeler la fonction avec les couleurs sélectionnées
        updateStatus('✨ Génération des tickets en cours...');
        updateProgress(30);

        const result = await pywebview.api.generate_renault_tickets_with_colors(selectedColors);

        if (result.success) {
            // Progression finale avec animation
            updateProgress(85);
            updateStatus('📁 Création des dossiers...');
            await new Promise(resolve => setTimeout(resolve, 300));

            updateProgress(95);
            updateStatus('🎯 Finalisation...');
            await new Promise(resolve => setTimeout(resolve, 200));

            updateProgress(100);
            updateStatus('✅ Tickets Renault générés avec succès !');

            // Notification de succès avec détails
            const colorNames = selectedColors.map(c => c === 'black' ? 'noir' : 'blanc').join(' et ');
            showNotification(`🎉 Tickets générés avec fond ${colorNames} !`, 'success');

            // Simuler generation_complete pour réactiver le bouton
            console.log('Succès tickets Renault - Simulation de generation_complete');
            generation_complete({success: true, message: `Tickets générés avec fond ${colorNames}`});
        } else {
            updateProgress(0);
            updateStatus('❌ Erreur lors de la génération des tickets');
            showNotification(result.message || 'Erreur inconnue', 'error');

            // Simuler generation_complete même en cas d'erreur
            console.log('Erreur tickets Renault - Simulation de generation_complete');
            generation_complete({success: false, message: result.message || 'Erreur inconnue'});
        }

    } catch (error) {
        console.error('Erreur:', error);
        updateProgress(0);
        updateStatus('❌ Erreur lors de la génération des tickets');
        showNotification('Erreur lors de la génération des tickets Renault', 'error');
    } finally {
        // FORCER LA RÉACTIVATION DU BOUTON D'IMPRESSION
        console.log('Réactivation du bouton d\'impression après génération Renault');
        forceEnablePrintButton();

        // Timer de sécurité pour forcer la réactivation après 3 secondes
        setTimeout(() => {
            console.log('Timer de sécurité Renault - Réactivation forcée du bouton d\'impression');
            forceEnablePrintButton();
        }, 3000);
    }
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Application SOMACA Native chargée');
    updateStatus('Prêt à générer les codes');

    // Vérifier que pywebview est disponible
    if (typeof pywebview !== 'undefined') {
        console.log('PyWebView API disponible');
    } else {
        console.warn('PyWebView API non disponible');
    }

    // Vérifier que la modal existe, sinon la créer
    let modal = document.getElementById('print-options-modal');
    if (!modal) {
        console.log('Modal print-options non trouvée au chargement, création...');
        modal = createPrintOptionsModal();
        if (modal) {
            console.log('Modal créée avec succès au chargement');
        } else {
            console.error('Échec de création de la modal au chargement');
        }
    } else {
        console.log('Modal print-options trouvée au chargement');
    }
});
