#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lancement direct de l'application SOMACA avec toutes les modifications
"""

import sys
import os

def main():
    print("🚀 LANCEMENT DE L'APPLICATION SOMACA MODIFIÉE")
    print("=" * 60)
    print("✅ Texte SOMACA agrandi (50pt → 70pt)")
    print("✅ Position SOMACA à 0.7cm du haut (au lieu de 0.5cm)")
    print("✅ Sous-titre 'Société Marocaine...' SUPPRIMÉ")
    print("✅ QR code agrandi (2.5cm → 4.0cm)")
    print("✅ Espace 0.8cm entre SOMACA et QR code (au lieu de 0.2cm)")
    print("✅ QR code centré horizontalement")
    print("=" * 60)
    print()
    
    # Vérifier les dépendances
    try:
        print("🔍 Vérification des dépendances...")
        import webview
        import pandas
        import openpyxl
        import qrcode
        import barcode
        from PIL import Image
        print("✅ Toutes les dépendances sont disponibles!")
        print()
    except ImportError as e:
        print(f"❌ Dépendance manquante: {e}")
        print("💡 Installez avec: pip install pywebview pandas openpyxl qrcode python-barcode Pillow")
        return False
    
    # Lancer l'application
    try:
        print("🚀 Démarrage de l'application...")
        
        # Importer le module principal
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # Lancer l'application native
        import app_native
        
        print("✅ Application lancée avec succès!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("\nAppuyez sur Entrée pour fermer...")
