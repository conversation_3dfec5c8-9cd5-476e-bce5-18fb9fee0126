#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour convertir exel.png en exel.ico multi-résolutions
Développé par: IMAD ELberrouagui
"""

from PIL import Image
import os

def convert_png_to_ico():
    """Convertir exel.png en exel.ico avec plusieurs résolutions"""
    
    # Chemin vers l'image source
    png_path = 'exel.png'
    ico_path = 'exel.ico'
    
    if not os.path.exists(png_path):
        png_path = os.path.join('..', 'exel.png')
    
    if not os.path.exists(png_path):
        print(f"❌ Fichier {png_path} non trouvé")
        return False
    
    try:
        print(f"🔄 Conversion de {png_path} en {ico_path}...")
        
        # Ouvrir l'image PNG
        img = Image.open(png_path)
        
        # Convertir en RGBA si nécessaire
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        
        # Tailles d'icônes Windows standard
        sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        
        # Créer les différentes tailles
        icon_images = []
        for size in sizes:
            resized = img.resize(size, Image.Resampling.LANCZOS)
            icon_images.append(resized)
            print(f"✅ Taille {size[0]}x{size[1]} créée")
        
        # Sauvegarder en .ico
        icon_images[0].save(
            ico_path,
            format='ICO',
            sizes=[(img.width, img.height) for img in icon_images],
            append_images=icon_images[1:]
        )
        
        print(f"✅ Fichier {ico_path} créé avec succès !")
        print(f"📊 Tailles incluses: {', '.join([f'{s[0]}x{s[1]}' for s in sizes])}")
        
        # Vérifier la taille du fichier
        if os.path.exists(ico_path):
            size_kb = os.path.getsize(ico_path) / 1024
            print(f"📁 Taille du fichier: {size_kb:.1f} KB")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la conversion: {e}")
        return False

if __name__ == '__main__':
    print("=" * 60)
    print("🏭 SOMACA - Générateur de Codes-Barres")
    print("🔄 Conversion PNG → ICO")
    print("👨‍💻 Développé par: IMAD ELberrouagui")
    print("=" * 60)
    
    success = convert_png_to_ico()
    
    if success:
        print("\n🎉 Conversion réussie !")
        print("💡 Vous pouvez maintenant utiliser exel.ico pour l'application")
        print("🚀 L'icône sera parfaite pour Windows !")
    else:
        print("\n❌ Échec de la conversion")
        print("💡 Vérifiez que le fichier exel.png existe")
