#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple pour vérifier la génération de codes-barres
"""

import barcode
from barcode.writer import ImageWriter
import io
from openpyxl import Workbook
from openpyxl.drawing.image import Image as OpenpyxlImage

def test_barcode_generation():
    """Test simple de génération de code-barre"""
    try:
        print("Test de génération de code-barre...")
        
        # Données de test simples (ASCII seulement)
        test_data = "TEST-123"
        
        # Obtenir la classe Code128
        code128_class = barcode.get_barcode_class('code128')
        print(f"Classe Code128 obtenue: {code128_class}")
        
        # Créer l'instance du code-barre
        barcode_instance = code128_class(test_data, writer=ImageWriter())
        print(f"Instance créée pour: {test_data}")
        
        # Générer l'image en mémoire
        buffer = io.BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)
        print(f"Image générée, taille buffer: {len(buffer.getvalue())} bytes")
        
        # Créer l'objet image pour Excel
        img = OpenpyxlImage(buffer)
        print(f"Image OpenpyXL créée: {img}")
        
        # Test avec un workbook
        workbook = Workbook()
        worksheet = workbook.active
        
        # Ajouter l'image
        img.anchor = "A1"
        worksheet.add_image(img)
        
        # Sauvegarder le test
        test_file = "test_barcode_output.xlsx"
        workbook.save(test_file)
        print(f"✅ Test réussi ! Fichier sauvé: {test_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_barcode_generation()
