#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lanceur simple pour l'application SOMACA
Vérifie les dépendances et lance l'application
"""

import sys
import subprocess
import os

def check_and_install_dependencies():
    """Vérifier et installer les dépendances si nécessaire"""
    required_packages = [
        'webview',
        'pandas', 
        'openpyxl',
        'qrcode',
        'barcode',
        'PIL'  # Pillow
    ]
    
    missing_packages = []
    
    print("🔍 Vérification des dépendances...")
    
    for package in required_packages:
        try:
            if package == 'barcode':
                __import__('barcode')
            elif package == 'PIL':
                __import__('PIL')
            else:
                __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package} manquant")
    
    if missing_packages:
        print(f"\n🔧 Installation de {len(missing_packages)} package(s) manquant(s)...")
        
        # Mapping des noms de packages pour pip
        pip_names = {
            'barcode': 'python-barcode',
            'PIL': 'Pillow',
            'webview': 'pywebview'
        }
        
        for package in missing_packages:
            pip_name = pip_names.get(package, package)
            print(f"  📦 Installation de {pip_name}...")
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', pip_name])
                print(f"  ✅ {pip_name} installé")
            except subprocess.CalledProcessError as e:
                print(f"  ❌ Erreur installation {pip_name}: {e}")
                return False
    
    print("✅ Toutes les dépendances sont prêtes!")
    return True

def launch_app():
    """Lancer l'application SOMACA"""
    print("\n🚀 Lancement de l'application SOMACA...")
    print("💡 Nouvelles fonctionnalités:")
    print("  • Texte SOMACA agrandi (70pt)")
    print("  • Position SOMACA à 0.7cm du haut")
    print("  • Sous-titre supprimé")
    print("  • QR code agrandi (4.0cm)")
    print("  • Espace 0.8cm entre SOMACA et QR code")
    print("  • QR code centré horizontalement")
    print()
    
    try:
        # Importer et lancer l'application
        import app_native
        print("✅ Application lancée avec succès!")
        
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        print("\n💡 Solutions possibles:")
        print("  1. Vérifiez que Python est correctement installé")
        print("  2. Essayez: python app_native.py")
        print("  3. Ou utilisez l'exécutable: .\\Lancer_SOMACA.bat")
        return False
    
    return True

if __name__ == "__main__":
    print("=" * 50)
    print("    SOMACA - Générateur QR & Code à Barre")
    print("    Version Native avec modifications")
    print("    Développé par: IMAD ELberrouagui")
    print("=" * 50)
    
    # Vérifier et installer les dépendances
    if check_and_install_dependencies():
        # Lancer l'application
        launch_app()
    else:
        print("\n❌ Impossible d'installer toutes les dépendances")
        print("💡 Essayez d'installer manuellement:")
        print("   pip install pywebview pandas openpyxl qrcode python-barcode Pillow")
    
    input("\nAppuyez sur Entrée pour fermer...")
