#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour vérifier la génération d'images encadrées
"""

import os
import sys
import pandas as pd
from app import SomacaBarcodeGenerator

def create_test_excel():
    """Créer un fichier Excel de test"""
    data = {
        'Numéro': ['12345', '67890', '11111'],
        'Référence': ['ABC-789', 'DEF-456', 'GHI-123'],
        'Description': ['Pièce de test 1', 'Pièce de test 2', 'Pièce de test 3'],
        'Quantité': [10, 5, 8],
        'Prix': [25.50, 15.75, 30.00]
    }
    
    df = pd.DataFrame(data)
    test_file = "test_framed_excel.xlsx"
    df.to_excel(test_file, index=False)
    print(f"✅ Fichier de test créé: {test_file}")
    return test_file

def test_framed_images():
    """Test de génération d'images encadrées"""
    
    # Créer le fichier de test
    test_file = create_test_excel()
    
    # Créer une instance du générateur
    generator = SomacaBarcodeGenerator()
    
    # Créer un dossier de test
    test_folder = "test_images_encadrees"
    os.makedirs(test_folder, exist_ok=True)
    
    # Configurer le générateur
    generator.set_input_file(test_file)
    generator.set_output_folder(test_folder)
    
    print("🧪 Test de génération d'images encadrées...")
    
    try:
        # Tester la génération d'images encadrées
        result = generator.generate_framed_images()
        
        if result["success"]:
            print(f"✅ {result['message']}")
            
            # Vérifier que les fichiers ont été créés
            images_folder = os.path.join(test_folder, "Images_Encadrees_6x3cm")
            if os.path.exists(images_folder):
                files = os.listdir(images_folder)
                png_files = [f for f in files if f.endswith('.png')]
                print(f"📁 {len(png_files)} images encadrées générées dans {images_folder}")
                for file in png_files:
                    print(f"   - {file}")
            else:
                print("❌ Dossier d'images non trouvé")
                
        else:
            print(f"❌ Erreur: {result['message']}")
            return False
            
    except Exception as e:
        print(f"💥 Erreur pendant le test: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Nettoyer le fichier de test
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"🧹 Fichier de test supprimé: {test_file}")
    
    return True

if __name__ == "__main__":
    try:
        success = test_framed_images()
        if success:
            print("\n🎉 Test réussi ! La fonction d'images encadrées fonctionne correctement.")
        else:
            print("\n❌ Test échoué. Vérifiez les erreurs ci-dessus.")
    except Exception as e:
        print(f"\n💥 Erreur pendant le test: {e}")
        import traceback
        traceback.print_exc()
