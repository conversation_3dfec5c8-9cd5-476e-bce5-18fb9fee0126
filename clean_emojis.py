#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour nettoyer tous les emojis et caractères Unicode problématiques
"""

import re
import os

def clean_emojis_from_file(file_path):
    """Nettoie seulement les emojis problématiques d'un fichier en préservant l'indentation"""

    # Dictionnaire de remplacement pour les emojis courants
    emoji_replacements = {
        '🔧': '',
        '🔍': '',
        '📂': '',
        '📁': '',
        '✅': '',
        '❌': '',
        '⚠️': '',
        '🚀': '',
        '📊': '',
        '📋': '',
        '📝': '',
        '📤': '',
        '📄': '',
        '🖨️': '',
        '📱': '',
        '📏': '',
        '🛑': '',
        '⏰': '',
        'ℹ️': '',
        '🎉': '',
        '🎯': '',
        '🌟': '',
        '🏭': '',
        '⏳': ''
    }

    print(f"Nettoyage des emojis dans: {file_path}")

    # Lire le fichier ligne par ligne pour préserver l'indentation
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    # Nettoyer chaque ligne
    cleaned_lines = []
    for line in lines:
        cleaned_line = line
        # Remplacer les emojis connus
        for emoji, replacement in emoji_replacements.items():
            cleaned_line = cleaned_line.replace(emoji, replacement)

        # Supprimer seulement les caractères emoji Unicode (U+1F000-U+1F9FF)
        cleaned_line = re.sub(r'[\U0001F000-\U0001F9FF]', '', cleaned_line)

        cleaned_lines.append(cleaned_line)

    # Sauvegarder le fichier nettoyé
    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(cleaned_lines)

    print(f"Fichier nettoyé: {file_path}")

if __name__ == "__main__":
    # Nettoyer le fichier app_native.py
    clean_emojis_from_file("app_native.py")
    print("Nettoyage terminé!")
