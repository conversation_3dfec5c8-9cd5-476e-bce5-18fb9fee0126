@echo off
echo ========================================
echo    SOMACA - Générateur QR & Code à Barre
echo    Version Native Windows
echo    Développé par: IMAD ELberrouagui
echo ========================================
echo.

echo Lancement de l'application...
cd /d "%~dp0"

if exist "dist\SOMACA_Native_Fixed.exe" (
    echo ✅ Application trouvée !
    echo 🚀 Démarrage en cours...
    echo.
    start "" "dist\SOMACA_Native_Fixed.exe"
    echo ✅ Application lancée avec succès !
    echo.
    echo 💡 L'application s'ouvre dans une nouvelle fenêtre
    echo 💡 Vous pouvez fermer cette console
    echo.
    pause
) else if exist "dist\SOMACA_Native.exe" (
    echo ✅ Application trouvée (version ancienne) !
    echo 🚀 Démarrage en cours...
    echo.
    start "" "dist\SOMACA_Native.exe"
    echo ✅ Application lancée avec succès !
    echo.
    echo 💡 L'application s'ouvre dans une nouvelle fenêtre
    echo 💡 Vous pouvez fermer cette console
    echo.
    pause
) else (
    echo ❌ Erreur: Aucun exécutable SOMACA trouvé dans le dossier 'dist'
    echo.
    echo 💡 Assurez-vous que l'application a été compilée
    echo 💡 Exécutez 'python build_quick.py' pour compiler
    echo.
    pause
)
