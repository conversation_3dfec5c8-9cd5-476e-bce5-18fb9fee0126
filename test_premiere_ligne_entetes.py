import pandas as pd

# <PERSON><PERSON>er un fichier Excel où la PREMIÈRE LIGNE de données devient les en-têtes
# et les lignes suivantes sont les données à traiter

data = [
    ['Numéro', 'Référence', 'Description', 'Catégorie'],  # Cette ligne devient les en-têtes
    ['S001', 'SERV-MAINT-001', 'Maintenance préventive', 'Service'],  # Données ligne 1
    ['S002', 'SERV-MAINT-002', 'Nettoyage système', 'Service'],       # Données ligne 2
    ['S003', 'SERV-REPAR-001', 'Réparation écran', 'Réparation']      # Données ligne 3
]

# Créer le DataFrame SANS en-têtes (toutes les lignes sont des données)
df = pd.DataFrame(data)

# Sauvegarder le fichier SANS en-têtes
output_path = 'test_premiere_ligne_entetes.xlsx'
df.to_excel(output_path, index=False, header=False)

print(f"Fichier de test créé: {output_path}")
print("Structure:")
for i, row in enumerate(data):
    if i == 0:
        print(f"Ligne {i+1} (devient en-têtes): {row}")
    else:
        print(f"Ligne {i+1} (données + codes): {row}")

print("\nRésultat attendu:")
print("En-têtes: Numéro | Référence | Description | Catégorie | Code-Barre | QR Code")
print("Données: S001 | SERV-MAINT-001 | Maintenance préventive | Service | [CODE] | [QR]")
print("         S002 | SERV-MAINT-002 | Nettoyage système | Service | [CODE] | [QR]")
print("         S003 | SERV-REPAR-001 | Réparation écran | Réparation | [CODE] | [QR]")
