#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script pour vérifier la modal de choix de couleurs
"""

import sys
import os

# Ajouter le répertoire parent au path pour importer app_native
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modal_colors():
    """Test de la modal de choix de couleurs"""
    try:
        from app_native import SomacaGenerator
        
        print("=== Test Modal Couleurs Tickets Renault ===")
        
        # Créer une instance du générateur
        generator = SomacaGenerator()
        
        # Simuler qu'un fichier Excel existe
        generator.last_generated_file = "test_somaca_simple.xlsx"
        
        # Test 1: Vérification que la modal s'affiche
        print("\n1. Test demande d'affichage modal:")
        result = generator.generate_renault_etiquettes()
        print(f"Résultat: {result}")
        
        if result.get('success') and result.get('action') == 'show_choice_modal':
            print("✅ Modal demandée correctement!")
        else:
            print("❌ Problème avec la demande de modal")
        
        # Test 2: Test génération avec couleurs multiples
        print("\n2. Test génération avec les deux couleurs:")
        result_multi = generator.generate_renault_tickets_with_colors(['black', 'white'])
        print(f"Résultat multi-couleurs: {result_multi.get('success', False)}")
        
        # Test 3: Test génération avec une seule couleur
        print("\n3. Test génération fond noir seulement:")
        result_black = generator.generate_renault_tickets_with_colors(['black'])
        print(f"Résultat fond noir: {result_black.get('success', False)}")
        
        print("\n=== Tests terminés ===")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_modal_colors()
