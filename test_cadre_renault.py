#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour créer des exemples d'images encadrées 6cm x 3cm avec logo Renault
"""

import os
from PIL import Image, ImageDraw, ImageFont
import qrcode
import barcode
from barcode.writer import ImageWriter
import io

def create_framed_barcode_example(barcode_number="64004380-0", part_number="1/2"):
    """Créer un exemple de code-barres encadré 6cm x 3cm avec logo Renault - Numéros configurables"""
    try:
        # Dimensions du cadre : 6cm x 3cm à 300 DPI
        frame_width = int(6.0 * 300 / 2.54)  # 709 pixels
        frame_height = int(3.0 * 300 / 2.54)  # 354 pixels

        print(f"📏 Dimensions du cadre: {frame_width}x{frame_height} pixels (6cm x 3cm)")
        print(f"🔢 Numéro code-barres: {barcode_number}")
        print(f"🔢 Numéro affiché: {barcode_number} {part_number}")

        # Créer l'image du cadre avec fond noir (exactement comme votre photo)
        frame_img = Image.new('RGB', (frame_width, frame_height), '#2a2a2a')
        draw = ImageDraw.Draw(frame_img)

        # Ajouter les trous jaunes dans les coins (comme des rivets)
        rivet_color = '#FFD700'  # Couleur dorée/jaune
        rivet_radius = 8
        margin = 15

        # Trou en haut à gauche
        draw.ellipse([margin-rivet_radius, margin-rivet_radius,
                     margin+rivet_radius, margin+rivet_radius], fill=rivet_color)

        # Trou en haut à droite
        draw.ellipse([frame_width-margin-rivet_radius, margin-rivet_radius,
                     frame_width-margin+rivet_radius, margin+rivet_radius], fill=rivet_color)

        # Trou en bas à gauche
        draw.ellipse([margin-rivet_radius, frame_height-margin-rivet_radius,
                     margin+rivet_radius, frame_height-margin+rivet_radius], fill=rivet_color)

        # Trou en bas à droite
        draw.ellipse([frame_width-margin-rivet_radius, frame_height-margin-rivet_radius,
                     frame_width-margin+rivet_radius, frame_height-margin+rivet_radius], fill=rivet_color)

        # Générer le code-barres SANS TEXTE (seulement les barres)
        code128 = barcode.get_barcode_class('code128')
        # Configuration pour supprimer le texte du code-barres
        writer = ImageWriter()
        writer.set_options({
            'write_text': False,  # Pas de texte dans le code-barres
            'text_distance': 0,
            'quiet_zone': 2
        })
        barcode_instance = code128(barcode_number, writer=writer)

        barcode_buffer = io.BytesIO()
        barcode_instance.write(barcode_buffer)
        barcode_buffer.seek(0)
        barcode_img = Image.open(barcode_buffer)

        # Charger le logo Renault (nouveau logo)
        logo_path = "renault-logo.png"
        if os.path.exists(logo_path):
            try:
                logo = Image.open(logo_path)
                # Redimensionner le logo (adapté au nouveau logo)
                logo_width = 100
                logo_height = 50
                logo = logo.resize((logo_width, logo_height), Image.Resampling.LANCZOS)

                # Positionner le logo AU-DESSUS du code-barres, centré
                logo_x = (frame_width - logo_width) // 2
                logo_y = 30  # En haut
                frame_img.paste(logo, (logo_x, logo_y), logo if logo.mode == 'RGBA' else None)
                print("✅ Logo Renault (nouveau) ajouté au-dessus du code-barres")
            except Exception as logo_error:
                print(f"⚠️ Erreur chargement logo: {logo_error}")
        else:
            print(f"⚠️ Logo non trouvé: {logo_path}")

        # Redimensionner le code-barres (seulement les barres, sans texte)
        barcode_width = 500  # Plus large pour bien remplir l'espace
        barcode_height = 70   # Plus fin car pas de texte intégré
        barcode_img_resized = barcode_img.resize((barcode_width, barcode_height), Image.Resampling.LANCZOS)

        # Positionner le code-barres au centre (sous le logo)
        barcode_x = (frame_width - barcode_width) // 2
        barcode_y = 120  # Sous le logo, plus d'espace pour le texte en bas
        frame_img.paste(barcode_img_resized, (barcode_x, barcode_y))

        # Charger la police pour le texte (plus gros pour le numéro)
        try:
            font_large = ImageFont.truetype("arial.ttf", 28)  # Plus gros pour le numéro
        except:
            font_large = ImageFont.load_default()

        # Ajouter le numéro en GROS en bas (comme dans votre photo)
        display_text = f"{barcode_number} {part_number}"
        bbox = draw.textbbox((0, 0), display_text, font=font_large)
        text_width = bbox[2] - bbox[0]
        text_x = (frame_width - text_width) // 2
        text_y = frame_height - 70  # En bas
        draw.text((text_x, text_y), display_text, fill='white', font=font_large)

        # Sauvegarder l'exemple
        save_path = f"exemple_barcode_{barcode_number.replace('-', '_')}.png"
        frame_img.save(save_path, 'PNG', dpi=(300, 300))
        print(f"🖼️ Exemple code-barres style Renault sauvé: {save_path}")
        return True

    except Exception as e:
        print(f"❌ Erreur création exemple code-barres: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_framed_qr_example(qr_data_custom=None, display_number="64004380-0", part_number="1/2"):
    """Créer un exemple de QR code encadré 6cm x 3cm avec logo Renault - Données configurables"""
    try:
        # Dimensions du cadre : 6cm x 3cm à 300 DPI
        frame_width = int(6.0 * 300 / 2.54)  # 709 pixels
        frame_height = int(3.0 * 300 / 2.54)  # 354 pixels

        # Données par défaut pour le QR code si non spécifiées
        if qr_data_custom is None:
            qr_data = f"Numéro:{display_number}|Référence:ABC-123|Description:Pièce test|Quantité:5"
        else:
            qr_data = qr_data_custom

        print(f"📱 QR code data: {qr_data[:50]}...")
        print(f"🔢 Numéro affiché: {display_number} {part_number}")

        # Créer l'image du cadre avec fond noir (exactement comme votre photo)
        frame_img = Image.new('RGB', (frame_width, frame_height), '#2a2a2a')
        draw = ImageDraw.Draw(frame_img)

        # Ajouter les trous jaunes dans les coins (comme des rivets)
        rivet_color = '#FFD700'  # Couleur dorée/jaune
        rivet_radius = 8
        margin = 15

        # Trou en haut à gauche
        draw.ellipse([margin-rivet_radius, margin-rivet_radius,
                     margin+rivet_radius, margin+rivet_radius], fill=rivet_color)

        # Trou en haut à droite
        draw.ellipse([frame_width-margin-rivet_radius, margin-rivet_radius,
                     frame_width-margin+rivet_radius, margin+rivet_radius], fill=rivet_color)

        # Trou en bas à gauche
        draw.ellipse([margin-rivet_radius, frame_height-margin-rivet_radius,
                     margin+rivet_radius, frame_height-margin+rivet_radius], fill=rivet_color)

        # Trou en bas à droite
        draw.ellipse([frame_width-margin-rivet_radius, frame_height-margin-rivet_radius,
                     frame_width-margin+rivet_radius, frame_height-margin+rivet_radius], fill=rivet_color)

        # Charger le logo Renault (nouveau logo)
        logo_path = "renault-logo.png"
        if os.path.exists(logo_path):
            try:
                logo = Image.open(logo_path)
                # Redimensionner le logo (adapté au nouveau logo)
                logo_width = 100
                logo_height = 50
                logo = logo.resize((logo_width, logo_height), Image.Resampling.LANCZOS)

                # Positionner le logo AU-DESSUS du QR code, centré
                logo_x = (frame_width - logo_width) // 2
                logo_y = 30  # En haut
                frame_img.paste(logo, (logo_x, logo_y), logo if logo.mode == 'RGBA' else None)
                print("✅ Logo Renault (nouveau) ajouté au-dessus du QR code")
            except Exception as logo_error:
                print(f"⚠️ Erreur chargement logo: {logo_error}")

        # Générer le QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=8,
            border=2,
        )
        qr.add_data(qr_data)
        qr.make(fit=True)
        qr_img = qr.make_image(fill_color="black", back_color="white")

        # Redimensionner le QR code (adapté à l'espace disponible)
        qr_size = 150  # Taille appropriée pour le QR code
        qr_img_resized = qr_img.resize((qr_size, qr_size), Image.Resampling.LANCZOS)

        # Positionner le QR code au centre (sous le logo)
        qr_x = (frame_width - qr_size) // 2
        qr_y = 95  # Sous le logo
        frame_img.paste(qr_img_resized, (qr_x, qr_y))

        # Charger la police pour le texte (plus gros pour le numéro)
        try:
            font_large = ImageFont.truetype("arial.ttf", 28)  # Plus gros pour le numéro
        except:
            font_large = ImageFont.load_default()

        # Ajouter le numéro en GROS en bas (comme dans votre photo)
        display_text = f"{display_number} {part_number}"
        bbox = draw.textbbox((0, 0), display_text, font=font_large)
        text_width = bbox[2] - bbox[0]
        text_x = (frame_width - text_width) // 2
        text_y = frame_height - 70  # En bas
        draw.text((text_x, text_y), display_text, fill='white', font=font_large)

        # Sauvegarder l'exemple
        save_path = f"exemple_qr_{display_number.replace('-', '_')}.png"
        frame_img.save(save_path, 'PNG', dpi=(300, 300))
        print(f"🖼️ Exemple QR code style Renault sauvé: {save_path}")
        return True

    except Exception as e:
        print(f"❌ Erreur création exemple QR code: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_different_numbers():
    """Tester avec différents numéros pour montrer la flexibilité"""
    print("🧪 Test avec différents numéros...")

    # Test avec différents numéros
    test_cases = [
        ("64004380-0", "1/2"),
        ("12345678-9", "2/3"),
        ("ABCD1234-X", "1/1")
    ]

    for barcode_num, part_num in test_cases:
        print(f"\n📏 Test code-barres: {barcode_num} {part_num}")
        create_framed_barcode_example(barcode_num, part_num)

        print(f"📱 Test QR code: {barcode_num} {part_num}")
        qr_data = f"Numéro:{barcode_num}|Référence:REF-{barcode_num[:4]}|Description:Pièce test|Quantité:10"
        create_framed_qr_example(qr_data, barcode_num, part_num)

def main():
    """Fonction principale pour créer les exemples"""
    print("🎨 Création d'exemples d'images encadrées 6cm x 3cm avec logo Renault")
    print("=" * 70)

    # Vérifier que le logo existe
    if not os.path.exists("renault-logo.png"):
        print("❌ Le fichier 'renault-logo.png' n'existe pas dans le répertoire courant")
        print("   Veuillez vous assurer que le logo Renault est présent")
        return False

    success = True

    # Créer l'exemple de code-barres encadré avec numéro par défaut
    print("\n📏 Création de l'exemple code-barres encadré (numéro par défaut)...")
    if not create_framed_barcode_example():
        success = False

    # Créer l'exemple de QR code encadré avec numéro par défaut
    print("\n📱 Création de l'exemple QR code encadré (numéro par défaut)...")
    if not create_framed_qr_example():
        success = False

    # Tester avec différents numéros
    print("\n" + "="*50)
    test_with_different_numbers()

    if success:
        print("\n🎉 Exemples créés avec succès !")
        print("📁 Fichiers générés:")
        print("   - exemple_barcode_64004380_0.png (numéro par défaut)")
        print("   - exemple_qr_64004380_0.png (numéro par défaut)")
        print("   - exemple_barcode_12345678_9.png (test)")
        print("   - exemple_qr_12345678_9.png (test)")
        print("   - exemple_barcode_ABCD1234_X.png (test)")
        print("   - exemple_qr_ABCD1234_X.png (test)")
        print("\n💡 Vérifiez ces exemples pour valider le design avant intégration")
        print("🎨 Style: Fond noir, logo Renault au-dessus, rivets dorés dans les coins")
        print("🔧 Numéros configurables: code-barres et texte affiché")
    else:
        print("\n❌ Erreurs lors de la création des exemples")

    return success

if __name__ == "__main__":
    main()
