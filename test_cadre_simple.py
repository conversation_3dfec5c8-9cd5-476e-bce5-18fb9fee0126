#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script simple pour tester un cadre Renault avec numéros personnalisés
"""

import os
from PIL import Image, ImageDraw, ImageFont
import qrcode
import barcode
from barcode.writer import ImageWriter
import io

def create_renault_frame(barcode_number, part_number="1/2", qr_data=None, output_type="barcode"):
    """
    Créer une image encadrée style Renault 6cm x 3cm
    
    Args:
        barcode_number: Le numéro pour le code-barres (ex: "64004380-0")
        part_number: Le numéro de partie (ex: "1/2")
        qr_data: Données pour QR code (optionnel, auto-généré si None)
        output_type: "barcode" ou "qr"
    """
    try:
        # Dimensions du cadre : 6cm x 3cm à 300 DPI
        frame_width = int(6.0 * 300 / 2.54)  # 708 pixels
        frame_height = int(3.0 * 300 / 2.54)  # 354 pixels
        
        print(f"🎨 Création {output_type} - {barcode_number} {part_number}")
        
        # Créer l'image du cadre avec fond noir
        frame_img = Image.new('RGB', (frame_width, frame_height), '#2a2a2a')
        draw = ImageDraw.Draw(frame_img)
        
        # Ajouter les rivets dorés dans les coins
        rivet_color = '#FFD700'
        rivet_radius = 8
        margin = 15
        
        # Dessiner les 4 rivets
        positions = [
            (margin, margin),  # Haut gauche
            (frame_width-margin, margin),  # Haut droite
            (margin, frame_height-margin),  # Bas gauche
            (frame_width-margin, frame_height-margin)  # Bas droite
        ]
        
        for x, y in positions:
            draw.ellipse([x-rivet_radius, y-rivet_radius, 
                         x+rivet_radius, y+rivet_radius], fill=rivet_color)
        
        # Charger et positionner le logo Renault
        logo_path = "renault-logo.png"
        if os.path.exists(logo_path):
            try:
                logo = Image.open(logo_path)
                logo_width = 100
                logo_height = 50
                logo = logo.resize((logo_width, logo_height), Image.Resampling.LANCZOS)
                
                # Logo centré en haut
                logo_x = (frame_width - logo_width) // 2
                logo_y = 30
                frame_img.paste(logo, (logo_x, logo_y), logo if logo.mode == 'RGBA' else None)
                print("✅ Logo Renault ajouté")
            except Exception as e:
                print(f"⚠️ Erreur logo: {e}")
        
        # Générer le contenu selon le type
        if output_type == "barcode":
            # Générer le code-barres
            code128 = barcode.get_barcode_class('code128')
            barcode_instance = code128(barcode_number, writer=ImageWriter())
            
            barcode_buffer = io.BytesIO()
            barcode_instance.write(barcode_buffer)
            barcode_buffer.seek(0)
            barcode_img = Image.open(barcode_buffer)
            
            # Redimensionner et positionner le code-barres
            barcode_width = 450
            barcode_height = 90
            barcode_img_resized = barcode_img.resize((barcode_width, barcode_height), Image.Resampling.LANCZOS)
            
            barcode_x = (frame_width - barcode_width) // 2
            barcode_y = 110
            frame_img.paste(barcode_img_resized, (barcode_x, barcode_y))
            
        elif output_type == "qr":
            # Générer le QR code
            if qr_data is None:
                qr_data = f"Numéro:{barcode_number}|Référence:REF-{barcode_number[:4]}|Description:Pièce Renault"
            
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=8,
                border=2,
            )
            qr.add_data(qr_data)
            qr.make(fit=True)
            qr_img = qr.make_image(fill_color="black", back_color="white")
            
            # Redimensionner et positionner le QR code
            qr_size = 150
            qr_img_resized = qr_img.resize((qr_size, qr_size), Image.Resampling.LANCZOS)
            
            qr_x = (frame_width - qr_size) // 2
            qr_y = 95
            frame_img.paste(qr_img_resized, (qr_x, qr_y))
        
        # Ajouter le texte en bas
        try:
            font_large = ImageFont.truetype("arial.ttf", 28)
        except:
            font_large = ImageFont.load_default()
        
        display_text = f"{barcode_number} {part_number}"
        bbox = draw.textbbox((0, 0), display_text, font=font_large)
        text_width = bbox[2] - bbox[0]
        text_x = (frame_width - text_width) // 2
        text_y = frame_height - 70
        draw.text((text_x, text_y), display_text, fill='white', font=font_large)
        
        # Sauvegarder
        safe_filename = barcode_number.replace('-', '_').replace('/', '_')
        save_path = f"cadre_renault_{output_type}_{safe_filename}_{part_number.replace('/', '_')}.png"
        frame_img.save(save_path, 'PNG', dpi=(300, 300))
        print(f"💾 Sauvé: {save_path}")
        
        return save_path
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Test avec des exemples personnalisés"""
    print("🎨 Test Cadre Renault - Numéros personnalisés")
    print("=" * 50)
    
    if not os.path.exists("renault-logo.png"):
        print("❌ Logo Renault manquant!")
        return
    
    # Exemples à tester
    examples = [
        ("64004380-0", "1/2"),
        ("12345678-9", "2/3"),
        ("TEST-001", "1/1")
    ]
    
    for number, part in examples:
        print(f"\n📦 Test avec {number} {part}")
        
        # Créer code-barres
        barcode_file = create_renault_frame(number, part, output_type="barcode")
        
        # Créer QR code
        qr_file = create_renault_frame(number, part, output_type="qr")
        
        if barcode_file and qr_file:
            print(f"✅ Créé: {barcode_file} et {qr_file}")
    
    print("\n🎉 Test terminé! Vérifiez les fichiers générés.")

if __name__ == "__main__":
    main()
