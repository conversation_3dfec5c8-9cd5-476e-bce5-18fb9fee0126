#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test avec VRAI code-barres comme sur les produits commerciaux
"""

import os
import base64
import barcode
from barcode.writer import ImageWriter
from PIL import Image
import io

def get_logo_base64():
    """Convertit le logo Renault en base64"""
    try:
        logo_paths = ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"]
        for logo_path in logo_paths:
            if os.path.exists(logo_path):
                with open(logo_path, "rb") as f:
                    logo_data = f.read()
                print(f"✅ Logo trouvé: {logo_path}")
                return base64.b64encode(logo_data).decode('utf-8')
        return ""
    except Exception as e:
        print(f"⚠️ Erreur: {e}")
        return ""

def generate_real_barcode_base64(code_value):
    """Génère un VRAI code-barres et le convertit en base64"""
    try:
        # Générer un vrai code-barres Code128
        code128 = barcode.get_barcode_class('code128')
        
        # Configuration pour un code-barres sans texte et sans marges
        writer = ImageWriter()
        writer.set_options({
            'write_text': False,    # Pas de texte
            'text_distance': 0,     # Pas d'espace pour texte
            'quiet_zone': 0,        # Pas de zone de silence
            'module_width': 0.3,    # Largeur des barres
            'module_height': 20,    # Hauteur des barres
            'background': 'white',  # Fond blanc
            'foreground': 'black',  # Barres noires
        })
        
        barcode_instance = code128(code_value, writer=writer)
        
        # Générer l'image
        buffer = io.BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)
        
        # Charger l'image et la traiter
        barcode_img = Image.open(buffer)
        
        # Convertir en base64
        img_buffer = io.BytesIO()
        barcode_img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        barcode_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
        
        print(f"✅ Vrai code-barres généré: {barcode_img.size}")
        return barcode_base64
        
    except Exception as e:
        print(f"❌ Erreur génération code-barres: {e}")
        return ""

def create_etiquette_vrai_barcode(code_value="64004380-0", part_number="1/2"):
    """Créer une étiquette avec VRAI code-barres commercial"""
    
    logo_base64 = get_logo_base64()
    barcode_base64 = generate_real_barcode_base64(code_value)
    
    html_content = f"""<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Étiquette Renault - Vrai Code-barres</title>
  <style>
    body {{
      margin: 0;
      padding: 20px;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
    }}
    .label {{
      width: 6cm;
      height: 3cm;
      background-color: #000;
      border-radius: 8px;
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      font-family: Arial, sans-serif;
      margin: 0 auto;
      box-sizing: border-box;
      overflow: hidden;
      padding: 4px;
    }}
    .logo img {{
      height: 50px;
    }}
    .code-number {{
      font-size: 14px;
      font-weight: bold;
    }}
    .barcode-container {{
      /* Zone blanche exacte : 3cm x 0.8cm */
      width: 3cm;
      height: 0.8cm;
      background: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      box-sizing: border-box;
      padding: 0;
      margin: 0;
    }}
    .barcode-img {{
      /* Le code-barres remplit EXACTEMENT la zone blanche */
      width: 100%;
      height: 100%;
      object-fit: fill;  /* Étirer pour remplir complètement */
      display: block;
      border-radius: 4px;
    }}
  </style>
</head>
<body>
  <div id="labels"></div>
  <script>
    const label = document.createElement("div");
    label.className = "label";
    label.innerHTML = `
      <div class="logo"><img src="data:image/jpeg;base64,{logo_base64}" alt="Renault"></div>
      <div class="barcode-container">
        <img class="barcode-img" src="data:image/png;base64,{barcode_base64}" alt="Code-barres">
      </div>
      <div class="code-number">{code_value} {part_number}</div>
    `;
    document.getElementById("labels").appendChild(label);
  </script>
</body>
</html>"""
    
    filename = f"etiquette_vrai_barcode_{code_value.replace('-', '_')}_{part_number.replace('/', '_')}.html"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Étiquette avec vrai code-barres créée: {filename}")
    return filename

def main():
    """Test vrai code-barres commercial"""
    print("🏷️ Test VRAI Code-barres (comme sur les produits)")
    print("=" * 55)
    
    # Vérifier qu'un logo existe
    logo_found = any(os.path.exists(path) for path in ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"])
    if not logo_found:
        print("❌ Logo Renault manquant!")
        return
    
    # Créer l'étiquette
    filename = create_etiquette_vrai_barcode("64004380-0", "1/2")
    
    if filename:
        print(f"📁 Fichier créé: {filename}")
        print("💡 Code-barres RÉEL généré avec la bibliothèque barcode")
        print("✅ Barres qui remplissent exactement la zone blanche")
        print("✅ Pas d'espaces sur les côtés")
        
        # Ouvrir automatiquement
        try:
            os.system(f"start {filename}")
        except:
            print("   (Ouvrez manuellement le fichier)")

if __name__ == "__main__":
    main()
