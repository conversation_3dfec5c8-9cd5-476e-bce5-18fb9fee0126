# 🏭 SOMACA - Générateur de Codes-Barres

Application web moderne pour générer des codes-barres et QR codes à partir de fichiers Excel.

## ✨ Fonctionnalités

- 📊 **Interface web moderne** avec CSS professionnel
- 📁 **Sélection de fichiers** Excel (.xlsx, .xls)
- 🏷️ **Génération de codes-barres** (Code128)
- 📱 **Génération de QR codes**
- 📈 **Barre de progression** en temps réel
- 🎨 **Design SOMACA/Renault** avec couleurs officielles
- 💻 **Conversion en .exe** pour distribution

## 🚀 Installation et Utilisation

### Option 1: Lancement direct
```bash
# Installer les dépendances
pip install -r requirements.txt

# Lancer l'application
python app.py
```

### Option 2: Créer un exécutable
```bash
# Méthode automatique
python build_exe.py

# Ou utiliser le script d'installation
installer.bat
```

## 📁 Structure du projet

```
somaca_web_app/
├── app.py              # Backend Python avec Eel
├── web/
│   ├── index.html      # Interface utilisateur
│   ├── style.css       # Styles SOMACA
│   └── script.js       # Logique frontend
├── requirements.txt    # Dépendances Python
├── build_exe.py       # Script de création .exe
└── README.md          # Documentation
```

## 🎨 Design

- **Couleurs**: Bleu SOMACA (#003366) et Jaune Renault (#FFD320)
- **Police**: Segoe UI pour un look moderne
- **Responsive**: S'adapte à toutes les tailles d'écran
- **Animations**: Transitions fluides et effets hover

## 🔧 Technologies

- **Backend**: Python + Eel
- **Frontend**: HTML5 + CSS3 + JavaScript
- **Génération**: python-barcode + qrcode
- **Excel**: pandas + openpyxl
- **Build**: PyInstaller

## 📋 Utilisation

1. **Sélectionner** un fichier Excel avec vos données
2. **Choisir** un dossier de destination
3. **Cocher** les options de génération souhaitées
4. **Cliquer** sur "Générer les Codes"
5. **Suivre** la progression en temps réel

## 🎯 Avantages de cette approche

✅ **Interface magnifique** comparable aux applications web modernes  
✅ **Facilité de maintenance** avec HTML/CSS/JS  
✅ **Responsive design** automatique  
✅ **Conversion .exe** possible avec PyInstaller  
✅ **Performance** excellente avec Eel  
✅ **Personnalisation** facile des styles  

## 🏗️ Conversion en .exe

L'application peut être convertie en exécutable Windows :

```bash
python build_exe.py
```

Le fichier `SOMACA_Generateur.exe` sera créé dans le dossier `dist/`

## 🎨 Personnalisation

Pour modifier l'apparence, éditez le fichier `web/style.css` :
- Couleurs dans les variables CSS (`:root`)
- Layouts dans les classes `.card`, `.grid`
- Animations dans les `@keyframes`

## 🔒 Sécurité

- Aucune donnée n'est envoyée sur internet
- Traitement local des fichiers Excel
- Interface web locale uniquement

---

**SOMACA - Société Marocaine de Construction Automobile • Groupe Renault**
