#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test des modifications du ticket Renault
- Suppression du sous-titre "Société Marocaine de Construction Automobile"
- Agrandissement de l'écriture "SOMACA" (70pt au lieu de 50pt)
- Position du nom "SOMACA" à 0.7cm du haut (au lieu de 0.5cm)
"""

import sys
import os

def test_ticket_renault_modifie():
    """Test des tickets Renault modifiés"""
    try:
        print("=== Test des modifications du ticket Renault ===")
        print("1. Suppression du sous-titre")
        print("2. Agrandissement de SOMACA (50pt → 70pt)")
        print("3. Position à 0.7cm du haut (au lieu de 0.5cm)")
        print()
        
        # Importer le générateur
        from app_native import SomacaGenerator
        
        # Créer une instance
        generator = SomacaGenerator()
        
        # Définir un fichier de test simple
        test_file = "test_ticket_renault_modifie.xlsx"
        
        # Créer un fichier Excel de test
        import pandas as pd
        test_data = {
            'Code': ['64004380-0', '12345678-9', 'ABCD1234-X'],
            'Partie': ['1/2', '2/3', '1/1'],
            'Description': ['Pièce A', 'Pièce B', 'Pièce C']
        }
        df = pd.DataFrame(test_data)
        df.to_excel(test_file, index=False)
        print(f"✅ Fichier de test créé: {test_file}")
        
        # Définir le fichier d'entrée
        generator.set_input_file(test_file)
        
        # Tester avec fond noir (style Renault classique)
        print("\n🔥 Test avec fond NOIR (style Renault):")
        result_black = generator.generate_renault_etiquettes_with_choice(
            include_barcodes=True, 
            include_qr=True, 
            background_color="black"
        )
        
        if result_black and result_black.get("success"):
            print("✅ Tickets fond noir générés avec succès!")
            print(f"📁 Dossier: {result_black.get('output_folder', 'Non spécifié')}")
        else:
            print("❌ Erreur lors de la génération des tickets fond noir")
            print(f"Erreur: {result_black.get('message', 'Inconnue') if result_black else 'Aucun résultat'}")
        
        # Tester avec fond blanc
        print("\n⚪ Test avec fond BLANC:")
        result_white = generator.generate_renault_etiquettes_with_choice(
            include_barcodes=True, 
            include_qr=True, 
            background_color="white"
        )
        
        if result_white and result_white.get("success"):
            print("✅ Tickets fond blanc générés avec succès!")
            print(f"📁 Dossier: {result_white.get('output_folder', 'Non spécifié')}")
        else:
            print("❌ Erreur lors de la génération des tickets fond blanc")
            print(f"Erreur: {result_white.get('message', 'Inconnue') if result_white else 'Aucun résultat'}")
        
        print("\n🎯 Vérifications à faire manuellement:")
        print("1. ✅ Le sous-titre 'Société Marocaine...' doit être SUPPRIMÉ")
        print("2. ✅ Le texte 'SOMACA' doit être plus GRAND (70pt)")
        print("3. ✅ Le texte 'SOMACA' doit être à 0.7cm du haut")
        print("4. ✅ Le cadre orange Renault doit être présent")
        print("5. ✅ Les codes-barres et QR codes doivent fonctionner")
        
        # Nettoyer le fichier de test
        try:
            os.remove(test_file)
            print(f"\n🧹 Fichier de test supprimé: {test_file}")
        except:
            pass
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Lancement du test des modifications du ticket Renault...")
    success = test_ticket_renault_modifie()
    
    if success:
        print("\n✅ Test terminé avec succès!")
        print("📋 Vérifiez les images générées pour confirmer les modifications")
    else:
        print("\n❌ Test échoué!")
        
    input("\nAppuyez sur Entrée pour fermer...")
