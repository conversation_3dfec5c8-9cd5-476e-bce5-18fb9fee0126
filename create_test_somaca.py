import pandas as pd

# Créer un fichier Excel de test avec la structure SOMACA exacte
data = {
    'Numéro': ['S001', 'S002', 'S003', 'S004'],
    'Référence': ['SERV-MAINT-001', 'SERV-MAINT-002', 'SERV-REPAR-001', 'SERV-REPAR-002'],
    'Description': [
        'Maintenance préventive ordinateurs',
        'Nettoyage système complet', 
        'Réparation écran cassé',
        'Remplacement disque dur'
    ]
}

df = pd.DataFrame(data)

# Sauvegarder le fichier
output_path = 'test_somaca_structure.xlsx'
df.to_excel(output_path, index=False)

print(f"Fichier de test SOMACA créé: {output_path}")
print("Structure:")
print(df)
print("\nCe fichier sera transformé en ajoutant les colonnes 'Code-Barre' et 'QR Code'")
