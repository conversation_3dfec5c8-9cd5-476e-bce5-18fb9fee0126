#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test avec dimensions identiques pour code-barres et QR code
"""

import os
import base64
import barcode
from barcode.writer import ImageWriter
import qrcode
from PIL import Image
import io

def get_logo_base64():
    """Convertit le logo Renault en base64"""
    try:
        logo_paths = ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"]
        for logo_path in logo_paths:
            if os.path.exists(logo_path):
                with open(logo_path, "rb") as f:
                    logo_data = f.read()
                print(f"✅ Logo trouvé: {logo_path}")
                return base64.b64encode(logo_data).decode('utf-8')
        return ""
    except Exception as e:
        print(f"⚠️ Erreur: {e}")
        return ""

def generate_clean_barcode_base64(code_value):
    """Génère un code-barres PROPRE sans texte"""
    try:
        # <PERSON><PERSON>érer code-barres sans texte
        code128 = barcode.get_barcode_class('code128')
        
        writer = ImageWriter()
        writer.set_options({
            'write_text': False,    # AUCUN texte
            'text_distance': 0,     
            'quiet_zone': 0.5,      # Zone minimale
            'module_width': 0.2,    
            'module_height': 15,    
            'background': 'white',  
            'foreground': 'black',  
        })
        
        barcode_instance = code128(code_value, writer=writer)
        
        buffer = io.BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)
        
        barcode_img = Image.open(buffer)
        
        # Convertir en base64
        img_buffer = io.BytesIO()
        barcode_img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        barcode_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
        
        print(f"✅ Code-barres propre généré: {barcode_img.size}")
        return barcode_base64
        
    except Exception as e:
        print(f"❌ Erreur code-barres: {e}")
        return ""

def generate_qr_base64(code_value):
    """Génère un QR code"""
    try:
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=8,
            border=2,
        )
        qr.add_data(code_value)
        qr.make(fit=True)
        
        qr_img = qr.make_image(fill_color="black", back_color="white")
        
        # Convertir en base64
        img_buffer = io.BytesIO()
        qr_img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        qr_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
        
        print(f"✅ QR code généré: {qr_img.size}")
        return qr_base64
        
    except Exception as e:
        print(f"❌ Erreur QR code: {e}")
        return ""

def create_etiquette_dimensions_identiques(code_value="64004380-0", part_number="1/2", code_type="barcode"):
    """Créer étiquette avec dimensions identiques"""
    
    logo_base64 = get_logo_base64()
    
    if code_type == "barcode":
        code_base64 = generate_clean_barcode_base64(code_value)
        code_title = "Code-barres"
    else:
        code_base64 = generate_qr_base64(code_value)
        code_title = "QR Code"
    
    html_content = f"""<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Étiquette Renault - {code_title}</title>
  <style>
    body {{
      margin: 0;
      padding: 20px;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
    }}
    .label {{
      width: 6cm;
      height: 3cm;
      background-color: #000;
      border-radius: 8px;
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      font-family: Arial, sans-serif;
      margin: 0 auto;
      box-sizing: border-box;
      overflow: hidden;
      padding: 4px;
    }}
    .logo img {{
      height: 50px;
    }}
    .code-number {{
      font-size: 14px;
      font-weight: bold;
    }}
    .code-container {{
      /* MÊMES dimensions pour code-barres ET QR code : 3cm x 0.8cm */
      width: 3cm;
      height: 0.8cm;
      background: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      box-sizing: border-box;
      padding: 0;
      margin: 0;
    }}
    .code-img {{
      /* Remplit EXACTEMENT la zone blanche */
      width: 100%;
      height: 100%;
      object-fit: fill;  /* Étirer pour remplir */
      display: block;
      border-radius: 4px;
    }}
  </style>
</head>
<body>
  <div id="labels"></div>
  <script>
    const label = document.createElement("div");
    label.className = "label";
    label.innerHTML = `
      <div class="logo"><img src="data:image/jpeg;base64,{logo_base64}" alt="Renault"></div>
      <div class="code-container">
        <img class="code-img" src="data:image/png;base64,{code_base64}" alt="{code_title}">
      </div>
      <div class="code-number">{code_value} {part_number}</div>
    `;
    document.getElementById("labels").appendChild(label);
  </script>
</body>
</html>"""
    
    filename = f"etiquette_{code_type}_{code_value.replace('-', '_')}_{part_number.replace('/', '_')}.html"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Étiquette {code_title} créée: {filename}")
    return filename

def main():
    """Test dimensions identiques"""
    print("📐 Test Dimensions Identiques (3cm x 0.8cm)")
    print("=" * 50)
    
    # Vérifier qu'un logo existe
    logo_found = any(os.path.exists(path) for path in ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"])
    if not logo_found:
        print("❌ Logo Renault manquant!")
        return
    
    # Créer les deux types d'étiquettes
    print("\n📊 Création code-barres...")
    barcode_file = create_etiquette_dimensions_identiques("64004380-0", "1/2", "barcode")
    
    print("\n📱 Création QR code...")
    qr_file = create_etiquette_dimensions_identiques("64004380-0", "1/2", "qr")
    
    if barcode_file and qr_file:
        print(f"\n🎉 Étiquettes créées:")
        print(f"📁 Code-barres: {barcode_file}")
        print(f"📁 QR code: {qr_file}")
        print("\n💡 Les deux ont les MÊMES dimensions: 3cm x 0.8cm")
        print("✅ Code-barres: seulement les barres, pas de texte")
        print("✅ UN SEUL numéro affiché en bas")
        
        # Ouvrir les fichiers
        try:
            os.system(f"start {barcode_file}")
            os.system(f"start {qr_file}")
        except:
            print("   (Ouvrez manuellement les fichiers)")

if __name__ == "__main__":
    main()
