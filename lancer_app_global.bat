@echo off
echo ========================================
echo    SOMACA - Générateur QR & Code à Barre
echo    Version Native avec Python Global
echo    Développé par: IMAD ELberrouagui
echo ========================================
echo.

echo Vérification des dépendances Python...
python -c "import webview, pandas, openpyxl, qrcode, barcode; print('✅ Toutes les dépendances sont installées')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Certaines dépendances manquent
    echo 🔧 Installation des dépendances...
    python -m pip install pywebview pandas openpyxl qrcode python-barcode Pillow
    echo.
)

echo 🚀 Lancement de l'application SOMACA...
echo 💡 Avec les nouvelles modifications:
echo   • Texte SOMACA agrandi (70pt)
echo   • Position SOMACA à 0.7cm du haut
echo   • QR code agrandi (4.0cm)
echo   • Espace 0.8cm entre SOMACA et QR code
echo   • QR code centré horizontalement
echo.

cd /d "%~dp0"
python app_native.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ Erreur lors du lancement de l'application
    echo 💡 Vérifiez que Python est installé et accessible
    pause
) else (
    echo.
    echo ✅ Application fermée normalement
)
