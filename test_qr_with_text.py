#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour vérifier la génération de QR codes et codes-barres avec texte descriptif
"""

import os
import sys
from app import SomacaBarcodeGenerator

def test_qr_and_barcode_with_text():
    """Test de génération de QR codes et codes-barres avec texte descriptif"""
    
    # Créer une instance du générateur
    generator = SomacaBarcodeGenerator()
    
    # Créer un dossier de test
    test_folder = "test_images_avec_texte"
    os.makedirs(test_folder, exist_ok=True)
    
    print("🧪 Test de génération d'images avec texte descriptif...")
    
    # Test 1: QR code avec texte
    print("\n📱 Test QR code avec texte...")
    qr_data = "Numéro:12345|Référence:ABC-789|Description:Pièce de test"
    qr_text = "Numéro: 12345\nRéférence: ABC-789"
    qr_save_path = os.path.join(test_folder, "test_qr_avec_texte.png")
    
    success_qr = generator.generate_high_res_qr_code_with_text(qr_data, qr_text, qr_save_path)
    if success_qr:
        print(f"✅ QR code avec texte généré: {qr_save_path}")
    else:
        print("❌ Échec génération QR code avec texte")
    
    # Test 2: Code-barres avec texte
    print("\n📏 Test code-barres avec texte...")
    barcode_data = "12345-ABC789"
    barcode_text = "Numéro: 12345\nRéférence: ABC-789"
    barcode_save_path = os.path.join(test_folder, "test_barcode_avec_texte.png")
    
    success_barcode = generator.generate_high_res_barcode_with_text(barcode_data, barcode_text, barcode_save_path)
    if success_barcode:
        print(f"✅ Code-barres avec texte généré: {barcode_save_path}")
    else:
        print("❌ Échec génération code-barres avec texte")
    
    # Test 3: Comparaison avec les versions sans texte
    print("\n🔄 Génération des versions sans texte pour comparaison...")
    
    # QR code sans texte
    qr_simple_path = os.path.join(test_folder, "test_qr_sans_texte.png")
    success_qr_simple = generator.generate_high_res_qr_code(qr_data, qr_simple_path)
    if success_qr_simple:
        print(f"✅ QR code simple généré: {qr_simple_path}")
    
    # Code-barres sans texte
    barcode_simple_path = os.path.join(test_folder, "test_barcode_sans_texte.png")
    success_barcode_simple = generator.generate_high_res_barcode(barcode_data, barcode_simple_path)
    if success_barcode_simple:
        print(f"✅ Code-barres simple généré: {barcode_simple_path}")
    
    print(f"\n📁 Tous les fichiers de test sont dans le dossier: {test_folder}")
    print("🔍 Vous pouvez maintenant comparer les images avec et sans texte descriptif.")
    
    return success_qr and success_barcode

if __name__ == "__main__":
    try:
        success = test_qr_and_barcode_with_text()
        if success:
            print("\n🎉 Test réussi ! Les nouvelles fonctionnalités fonctionnent correctement.")
        else:
            print("\n❌ Test échoué. Vérifiez les erreurs ci-dessus.")
    except Exception as e:
        print(f"\n💥 Erreur pendant le test: {e}")
        import traceback
        traceback.print_exc()
