import pandas as pd

# Test 1: Fichier avec en-têtes "Immobilisation" etc.
data1 = [
    ['Immobilisation', 'Numéro subsidiaire', 'Designation', 'Date'],
    ['64009423', '0', 'abies élévatrice', '2024-05-29'],
    ['64604200', '3', 'MBALLAGES CDP', '2024-01-01']
]

# Test 2: Fichier avec en-têtes complètement différents
data2 = [
    ['Code', 'Ref', 'Description', 'Statut', 'Priorité'],
    ['S001', 'SERV-001', 'Maintenance', 'Actif', 'Haute'],
    ['S002', 'SERV-002', 'Réparation', 'En cours', 'Moyenne']
]

# Test 3: Fichier avec seulement 3 colonnes
data3 = [
    ['Numéro', 'Référence', 'Description'],
    ['001', 'REF-001', 'Test 1'],
    ['002', 'REF-002', 'Test 2']
]

# Créer les fichiers de test
for i, data in enumerate([data1, data2, data3], 1):
    df = pd.DataFrame(data)
    filename = f'test_adaptatif_{i}.xlsx'
    df.to_excel(filename, index=False, header=False)
    print(f"Fichier créé: {filename}")
    print(f"En-têtes: {data[0]}")
    print(f"Données: {len(data)-1} lignes")
    print()

print("✅ Tous les fichiers de test créés")
print("L'application doit s'adapter automatiquement à chaque structure !")
