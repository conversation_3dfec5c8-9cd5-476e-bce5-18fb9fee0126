import pandas as pd

# Créer un fichier Excel avec la structure EXACTE de votre exemple
data = {
    'Immobilisation': ['64009423', '64604200', '64801811'],
    'Numéro subsidiaire': ['0', '3', '0'],
    'Designation de l\'immobilisation': [
        'abies élévatrice retouche',
        'MBALLAGES CDP X52 SOW',
        'Scanner QR code Affibag'
    ],
    'Date mise en service': [
        '2024-05-29 00:00:00',
        '2024-01-01 00:00:00',
        '2024-03-15 00:00:00'
    ]
}

df = pd.DataFrame(data)

# Sauvegarder le fichier avec les en-têtes corrects
output_path = 'test_structure_correcte.xlsx'
df.to_excel(output_path, index=False)

print(f"Fichier de test créé: {output_path}")
print("Structure:")
print(df)
print("\nRésultat attendu:")
print("En-têtes: Immobilisation | Numéro subsidiaire | Designation de l'immobilisation | Date mise en service | Code-Barre | QR Code")
print("Données avec codes-barres et QR codes pour chaque ligne")
print("\nCodes générés:")
print("- Code-barres: 64009423-0 (2 premières colonnes)")
print("- QR Code: COL1:64009423|COL2:0|COL3:abies élévatrice retouche|COL4:2024-05-29 00:00:00 (toutes les colonnes)")
