#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour vérifier que le problème de conflit de fichiers est résolu
Développé par Imad Elberrouagui
"""

import os
import sys
import tempfile
import pandas as pd
from app_native import SomacaCodeGenerator

def create_test_excel_file(filename):
    """Créer un fichier Excel de test"""
    data = {
        'Nom': ['Test1', 'Test2', 'Test3'],
        'Code': ['ABC123', 'DEF456', 'GHI789'],
        'Description': ['Description 1', 'Description 2', 'Description 3']
    }
    df = pd.DataFrame(data)
    df.to_excel(filename, index=False)
    print(f"✅ Fichier de test créé: {filename}")

def test_multiple_generations():
    """Tester plusieurs générations successives"""
    print("🧪 Test de génération multiple de fichiers Excel...")
    
    # Créer un dossier temporaire
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Dossier temporaire: {temp_dir}")
        
        # Créer deux fichiers Excel de test
        file1 = os.path.join(temp_dir, "test1.xlsx")
        file2 = os.path.join(temp_dir, "test2.xlsx")
        
        create_test_excel_file(file1)
        create_test_excel_file(file2)
        
        # Créer le générateur
        generator = SomacaCodeGenerator()
        generator.set_output_folder(temp_dir)
        
        print("\n🔄 Test 1: Génération avec le premier fichier...")
        generator.set_input_file(file1)
        result1 = generator.generate_codes(generate_barcodes=True, generate_qr=True)
        
        if result1["success"]:
            print("✅ Première génération réussie")
        else:
            print(f"❌ Première génération échouée: {result1['message']}")
            return False
        
        print("\n🔄 Test 2: Génération avec le deuxième fichier (test du conflit)...")
        generator.set_input_file(file2)
        result2 = generator.generate_codes(generate_barcodes=True, generate_qr=True)
        
        if result2["success"]:
            print("✅ Deuxième génération réussie - Problème résolu !")
            return True
        else:
            print(f"❌ Deuxième génération échouée: {result2['message']}")
            if "I/O operation on closed file" in result2['message']:
                print("❌ Le problème de conflit de fichiers persiste")
            return False

def test_same_file_multiple_times():
    """Tester la génération multiple avec le même fichier"""
    print("\n🧪 Test de génération multiple avec le même fichier...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Dossier temporaire: {temp_dir}")
        
        # Créer un fichier Excel de test
        test_file = os.path.join(temp_dir, "test_same.xlsx")
        create_test_excel_file(test_file)
        
        # Créer le générateur
        generator = SomacaCodeGenerator()
        generator.set_output_folder(temp_dir)
        generator.set_input_file(test_file)
        
        # Générer plusieurs fois
        for i in range(3):
            print(f"\n🔄 Génération #{i+1}...")
            result = generator.generate_codes(generate_barcodes=True, generate_qr=True)
            
            if result["success"]:
                print(f"✅ Génération #{i+1} réussie")
            else:
                print(f"❌ Génération #{i+1} échouée: {result['message']}")
                return False
        
        print("✅ Toutes les générations multiples ont réussi !")
        return True

if __name__ == "__main__":
    print("🚀 Test de résolution du conflit de fichiers Excel")
    print("=" * 60)
    
    try:
        # Test 1: Fichiers différents
        success1 = test_multiple_generations()
        
        # Test 2: Même fichier plusieurs fois
        success2 = test_same_file_multiple_times()
        
        print("\n" + "=" * 60)
        if success1 and success2:
            print("🎉 TOUS LES TESTS RÉUSSIS - Le problème est résolu !")
        else:
            print("❌ CERTAINS TESTS ONT ÉCHOUÉ - Le problème persiste")
            
    except Exception as e:
        print(f"❌ Erreur durant les tests: {e}")
        import traceback
        traceback.print_exc()
