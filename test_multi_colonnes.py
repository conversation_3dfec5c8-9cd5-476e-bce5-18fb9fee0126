import pandas as pd

# Créer un fichier Excel de test avec PLUSIEURS colonnes
data = {
    'Numéro': ['S001', 'S002', 'S003'],
    'Référence': ['SERV-MAINT-001', 'SERV-MAINT-002', 'SERV-REPAR-001'],
    'Description': ['Maintenance préventive', 'Nettoyage système', 'Réparation écran'],
    'Catégorie': ['Maintenance', 'Maintenance', 'Réparation'],
    'Priorité': ['Haute', 'Moyenne', 'Urgente'],
    'Technicien': ['<PERSON>', '<PERSON><PERSON>', '<PERSON>']
}

df = pd.DataFrame(data)

# Sauvegarder le fichier
output_path = 'test_6_colonnes.xlsx'
df.to_excel(output_path, index=False)

print(f"Fichier de test créé: {output_path}")
print("Structure avec 6 colonnes:")
print(df)
print("\nRésultat attendu:")
print("- Code-barres: S001-SERV-MAINT-001 (2 premières colonnes)")
print("- QR Code: COL1:S001|COL2:SERV-MAINT-001|COL3:Maintenance préventive|COL4:Maintenance|COL5:Haute|COL6:Ahmed (toutes les colonnes)")
