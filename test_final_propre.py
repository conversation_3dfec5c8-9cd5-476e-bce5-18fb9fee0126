#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Solution finale : Code-barres SEULEMENT barres + QR code même dimension
"""

import os
import base64
import barcode
from barcode.writer import ImageWriter
from PIL import Image, ImageDraw
import qrcode
import io

def get_logo_base64():
    """Convertit le logo Renault en base64"""
    try:
        logo_paths = ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"]
        for logo_path in logo_paths:
            if os.path.exists(logo_path):
                with open(logo_path, "rb") as f:
                    logo_data = f.read()
                print(f"✅ Logo trouvé: {logo_path}")
                return base64.b64encode(logo_data).decode('utf-8')
        return ""
    except Exception as e:
        print(f"⚠️ Erreur: {e}")
        return ""

def generate_clean_barcode_base64(code_value):
    """Génère un code-barres PROPRE (seulement barres) et le convertit en base64"""
    try:
        # <PERSON><PERSON><PERSON><PERSON> le code-barres
        code128 = barcode.get_barcode_class('code128')
        writer = ImageWriter()
        barcode_instance = code128(code_value, writer=writer)
        
        # Générer l'image
        buffer = io.BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)
        barcode_img = Image.open(buffer)
        
        # Analyser l'image pour trouver seulement les barres
        width, height = barcode_img.size
        
        # Convertir en RGB si nécessaire
        if barcode_img.mode != 'RGB':
            barcode_img = barcode_img.convert('RGB')
        
        # Trouver la zone des barres (sans texte)
        pixels = list(barcode_img.getdata())
        
        # Trouver la première et dernière ligne avec des barres noires
        first_bar_line = 0
        last_bar_line = height - 1
        
        for y in range(height):
            line_has_black = False
            for x in range(width):
                pixel_index = y * width + x
                if pixel_index < len(pixels):
                    r, g, b = pixels[pixel_index][:3]
                    if r < 128 and g < 128 and b < 128:  # Pixel noir
                        line_has_black = True
                        break
            
            if line_has_black:
                if first_bar_line == 0:
                    first_bar_line = y
                last_bar_line = y
        
        # Extraire SEULEMENT la zone des barres
        margin = 2
        crop_top = max(0, first_bar_line - margin)
        crop_bottom = min(height, last_bar_line + margin)
        
        # Découper pour garder seulement les barres
        clean_barcode = barcode_img.crop((0, crop_top, width, crop_bottom))
        
        # Convertir en base64
        img_buffer = io.BytesIO()
        clean_barcode.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        barcode_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
        
        print(f"✅ Code-barres propre généré: {clean_barcode.size}")
        return barcode_base64
        
    except Exception as e:
        print(f"❌ Erreur génération code-barres: {e}")
        return ""

def generate_qr_base64(code_value):
    """Génère un QR code et le convertit en base64"""
    try:
        # Générer le QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=2,
        )
        qr.add_data(code_value)
        qr.make(fit=True)
        
        qr_img = qr.make_image(fill_color="black", back_color="white")
        
        # Convertir en base64
        img_buffer = io.BytesIO()
        qr_img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        qr_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
        
        print(f"✅ QR code généré: {qr_img.size}")
        return qr_base64
        
    except Exception as e:
        print(f"❌ Erreur génération QR code: {e}")
        return ""

def create_etiquette_finale(code_value="64004380-0", part_number="1/2", code_type="barcode"):
    """Créer l'étiquette finale avec code propre"""
    
    logo_base64 = get_logo_base64()
    
    if code_type == "barcode":
        code_base64 = generate_clean_barcode_base64(code_value)
        code_title = "Code-barres"
    else:
        code_base64 = generate_qr_base64(code_value)
        code_title = "QR Code"
    
    html_content = f"""<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Étiquette Renault - {code_title} Final</title>
  <style>
    body {{
      margin: 0;
      padding: 20px;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
    }}
    .label {{
      width: 6cm;
      height: 3cm;
      background-color: #000;
      border-radius: 8px;
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      font-family: Arial, sans-serif;
      margin: 0 auto;
      box-sizing: border-box;
      overflow: hidden;
      padding: 4px;
    }}
    .logo img {{
      height: 50px;
    }}
    .code-number {{
      font-size: 14px;
      font-weight: bold;
    }}
    .code-container {{
      /* Zone blanche MÊME DIMENSION pour barcode ET QR : 3cm x 0.8cm */
      width: 3cm;
      height: 0.8cm;
      background: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      box-sizing: border-box;
      padding: 0;
      margin: 0;
    }}
    .code-img {{
      /* Le code remplit EXACTEMENT la zone blanche */
      width: 100%;
      height: 100%;
      object-fit: fill;  /* Étirer pour remplir complètement */
      display: block;
      border-radius: 4px;
    }}
  </style>
</head>
<body>
  <div id="labels"></div>
  <script>
    const label = document.createElement("div");
    label.className = "label";
    label.innerHTML = `
      <div class="logo"><img src="data:image/jpeg;base64,{logo_base64}" alt="Renault"></div>
      <div class="code-container">
        <img class="code-img" src="data:image/png;base64,{code_base64}" alt="{code_title}">
      </div>
      <div class="code-number">{code_value} {part_number}</div>
    `;
    document.getElementById("labels").appendChild(label);
  </script>
</body>
</html>"""
    
    filename = f"etiquette_finale_{code_type}_{code_value.replace('-', '_')}_{part_number.replace('/', '_')}.html"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Étiquette finale {code_type} créée: {filename}")
    return filename

def main():
    """Test solution finale"""
    print("🎯 SOLUTION FINALE - Code propre + Dimensions identiques")
    print("=" * 60)
    
    # Vérifier qu'un logo existe
    logo_found = any(os.path.exists(path) for path in ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"])
    if not logo_found:
        print("❌ Logo Renault manquant!")
        return
    
    # Créer les deux types d'étiquettes
    print("\n📊 Création étiquette CODE-BARRES...")
    barcode_file = create_etiquette_finale("64004380-0", "1/2", "barcode")
    
    print("\n📱 Création étiquette QR CODE...")
    qr_file = create_etiquette_finale("64004380-0", "1/2", "qr")
    
    print(f"\n🎉 Solution finale créée!")
    print(f"📁 Fichiers:")
    print(f"   - {barcode_file}")
    print(f"   - {qr_file}")
    print("\n💡 Caractéristiques:")
    print("   ✅ Code-barres: SEULEMENT les barres (pas de texte)")
    print("   ✅ QR code: MÊME dimensions que le code-barres")
    print("   ✅ Zone blanche: 3cm x 0.8cm pour les deux")
    print("   ✅ UN SEUL numéro affiché en bas")
    
    # Ouvrir les fichiers
    try:
        os.system(f"start {barcode_file}")
        os.system(f"start {qr_file}")
    except:
        print("   (Ouvrez manuellement les fichiers)")

if __name__ == "__main__":
    main()
