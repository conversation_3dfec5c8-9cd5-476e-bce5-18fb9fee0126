#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test des étiquettes basées sur votre template HTML
"""

import os
import base64
from PIL import Image, ImageDraw, ImageFont
import qrcode

def get_logo_base64():
    """Convertit le logo Renault en base64"""
    try:
        # Essayer différents formats de logo
        logo_paths = ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"]
        for logo_path in logo_paths:
            if os.path.exists(logo_path):
                with open(logo_path, "rb") as f:
                    logo_data = f.read()
                print(f"✅ Logo trouvé: {logo_path}")
                return base64.b64encode(logo_data).decode('utf-8')

        print("⚠️ Aucun logo trouvé")
        return ""
    except Exception as e:
        print(f"⚠️ Erreur conversion logo: {e}")
        return ""

def create_html_etiquette(code_value="64004380-0", part_number="1/2", code_type="barcode"):
    """Crée un fichier HTML d'étiquette basé sur votre template"""
    
    logo_base64 = get_logo_base64()
    
    html_content = f"""<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Étiquette Renault Moderne</title>
  <style>
    body {{
      margin: 0;
      padding: 20px;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
    }}
    .label {{
      width: 6cm;
      height: 3cm;
      background-color: #000;
      border-radius: 8px;
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      font-family: Arial, sans-serif;
      margin: 0 auto;
      box-sizing: border-box;
      overflow: hidden;
      padding: 4px;
    }}
    .logo img {{
      height: 40px;
    }}
    .code-number {{
      font-size: 12px;
      font-weight: bold;
    }}
    .barcode {{
      background: #fff;
      color: #000;
      font-family: 'Libre Barcode 39', cursive;
      font-size: 28px;
      border-radius: 4px;
      display: inline-block;
      padding: 2px 6px;
      max-width: 100%;
      white-space: nowrap;
    }}
    .qrcode canvas {{
      background: #fff;
      border-radius: 4px;
      width: 80%;
    }}
  </style>
  <script src="https://cdn.jsdelivr.net/npm/qrcode/build/qrcode.min.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Libre+Barcode+39&display=swap" rel="stylesheet">
</head>
<body>
  <div id="labels"></div>
  <script>
    const showBarcode = {1 if code_type == "barcode" else 0};
    const showQR = {1 if code_type == "qr" else 0};
    const codeValue = "{code_value}";

    if (showBarcode) {{
      const label = document.createElement("div");
      label.className = "label";
      label.innerHTML = `
        <div class="logo"><img src="data:image/jpeg;base64,{logo_base64}" alt="Renault"></div>
        <div class="barcode">*${{codeValue}}*</div>
        <div class="code-number">{code_value} {part_number}</div>
      `;
      document.getElementById("labels").appendChild(label);
    }}

    if (showQR) {{
      const label = document.createElement("div");
      label.className = "label";
      label.innerHTML = `
        <div class="logo"><img src="data:image/jpeg;base64,{logo_base64}" alt="Renault"></div>
        <div class="qrcode"></div>
        <div class="code-number">{code_value} {part_number}</div>
      `;
      document.getElementById("labels").appendChild(label);
      const canvas = document.createElement("canvas");
      label.querySelector(".qrcode").appendChild(canvas);
      QRCode.toCanvas(canvas, codeValue, {{ width: 80, color: {{ dark: "#000000", light: "#ffffff" }} }}, function(error) {{
        if (error) console.error(error);
      }});
    }}
  </script>
</body>
</html>"""
    
    # Sauvegarder le fichier HTML
    filename = f"etiquette_{code_type}_{code_value.replace('-', '_')}_{part_number.replace('/', '_')}.html"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Fichier HTML créé: {filename}")
    return filename

def create_python_based_etiquette(code_value="64004380-0", part_number="1/2", code_type="barcode"):
    """Crée une étiquette directement avec Python/PIL (style de votre HTML)"""
    try:
        # Dimensions : 6cm x 3cm à 300 DPI
        width = int(6.0 * 300 / 2.54)  # 708 pixels
        height = int(3.0 * 300 / 2.54)  # 354 pixels
        
        # Créer l'image avec fond noir
        img = Image.new('RGB', (width, height), '#2a2a2a')
        draw = ImageDraw.Draw(img)
        
        # Ajouter les rivets dorés dans les coins
        rivet_color = '#FFD700'
        rivet_radius = 4
        margin = 8
        
        positions = [
            (margin, margin),  # Haut gauche
            (width-margin, margin),  # Haut droite
            (margin, height-margin),  # Bas gauche
            (width-margin, height-margin)  # Bas droite
        ]
        
        for x, y in positions:
            draw.ellipse([x-rivet_radius, y-rivet_radius, 
                         x+rivet_radius, y+rivet_radius], fill=rivet_color)
        
        # Charger et ajouter le logo Renault
        logo_paths = ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"]
        logo_loaded = False

        for logo_path in logo_paths:
            if os.path.exists(logo_path):
                try:
                    logo = Image.open(logo_path)
                    logo_width = 80
                    logo_height = 40
                    logo = logo.resize((logo_width, logo_height), Image.Resampling.LANCZOS)

                    logo_x = (width - logo_width) // 2
                    logo_y = 20
                    img.paste(logo, (logo_x, logo_y), logo if logo.mode == 'RGBA' else None)
                    print(f"✅ Logo ajouté: {logo_path}")
                    logo_loaded = True
                    break
                except Exception as e:
                    print(f"⚠️ Erreur logo {logo_path}: {e}")

        if not logo_loaded:
            print("⚠️ Aucun logo trouvé")
        
        # Ajouter le code-barres ou QR code
        if code_type == "barcode":
            # Simuler un code-barres avec des rectangles
            barcode_width = 400
            barcode_height = 60
            barcode_x = (width - barcode_width) // 2
            barcode_y = 80
            
            # Fond blanc pour le code-barres
            draw.rectangle([barcode_x-5, barcode_y-5, barcode_x+barcode_width+5, barcode_y+barcode_height+5], fill='white')
            
            # Simuler les barres (pattern simple)
            bar_width = 2
            space_width = 1
            x_pos = barcode_x
            
            # Pattern simple pour simuler un code-barres
            pattern = "101011010110101101011010110101101011010110101101011010110101"
            for i, bit in enumerate(pattern):
                if bit == '1' and x_pos < barcode_x + barcode_width:
                    draw.rectangle([x_pos, barcode_y, x_pos+bar_width, barcode_y+barcode_height], fill='black')
                x_pos += bar_width + space_width
                
        elif code_type == "qr":
            # Générer un vrai QR code
            qr = qrcode.QRCode(version=1, error_correction=qrcode.constants.ERROR_CORRECT_L, box_size=4, border=2)
            qr.add_data(code_value)
            qr.make(fit=True)
            qr_img = qr.make_image(fill_color="black", back_color="white")
            
            # Redimensionner et positionner
            qr_size = 100
            qr_img_resized = qr_img.resize((qr_size, qr_size), Image.Resampling.LANCZOS)
            qr_x = (width - qr_size) // 2
            qr_y = 80
            img.paste(qr_img_resized, (qr_x, qr_y))
        
        # Ajouter le texte en bas (UN SEUL)
        try:
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            font = ImageFont.load_default()
        
        text = f"{code_value} {part_number}"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_x = (width - text_width) // 2
        text_y = height - 50
        draw.text((text_x, text_y), text, fill='white', font=font)
        
        # Sauvegarder
        filename = f"etiquette_python_{code_type}_{code_value.replace('-', '_')}_{part_number.replace('/', '_')}.png"
        img.save(filename, 'PNG', dpi=(300, 300))
        print(f"✅ Étiquette Python créée: {filename}")
        
        return filename
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def main():
    """Test des deux approches"""
    print("🎨 Test Étiquettes basées sur votre template HTML")
    print("=" * 60)
    
    # Vérifier qu'un logo existe
    logo_found = any(os.path.exists(path) for path in ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"])
    if not logo_found:
        print("❌ Logo Renault manquant!")
        return
    
    # Test avec différents codes
    test_cases = [
        ("64004380-0", "1/2"),
        ("12345678-9", "2/3")
    ]
    
    for code_value, part_number in test_cases:
        print(f"\n📦 Test avec {code_value} {part_number}")
        
        # Créer les fichiers HTML
        html_barcode = create_html_etiquette(code_value, part_number, "barcode")
        html_qr = create_html_etiquette(code_value, part_number, "qr")
        
        # Créer les images Python
        python_barcode = create_python_based_etiquette(code_value, part_number, "barcode")
        python_qr = create_python_based_etiquette(code_value, part_number, "qr")
    
    print("\n🎉 Test terminé!")
    print("📁 Fichiers créés:")
    print("   - Fichiers HTML (ouvrez dans un navigateur)")
    print("   - Images PNG (version Python)")
    print("\n💡 Ouvrez les fichiers HTML dans votre navigateur pour voir le rendu exact")

if __name__ == "__main__":
    main()
