#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test rapide avec logo agrandi
"""

import os
import base64

def get_logo_base64():
    """Convertit le logo Renault en base64"""
    try:
        logo_paths = ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"]
        for logo_path in logo_paths:
            if os.path.exists(logo_path):
                with open(logo_path, "rb") as f:
                    logo_data = f.read()
                print(f"✅ Logo trouvé: {logo_path}")
                return base64.b64encode(logo_data).decode('utf-8')
        return ""
    except Exception as e:
        print(f"⚠️ Erreur: {e}")
        return ""

def create_etiquette_logo_agrandi(code_value="64004380-0", part_number="1/2"):
    """Créer une étiquette avec logo agrandi"""
    
    logo_base64 = get_logo_base64()
    
    html_content = f"""<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Étiquette Renault - Logo Agrandi</title>
  <style>
    body {{
      margin: 0;
      padding: 20px;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
    }}
    .label {{
      width: 6cm;
      height: 3cm;
      background-color: #000;
      border-radius: 8px;
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      font-family: Arial, sans-serif;
      margin: 0 auto;
      box-sizing: border-box;
      overflow: hidden;
      padding: 4px;
    }}
    .logo img {{
      height: 50px;  /* Logo encore plus grand */
    }}
    .code-number {{
      font-size: 14px;
      font-weight: bold;
    }}
    .barcode {{
      background: #fff;
      color: #000;
      font-family: 'Libre Barcode 39', cursive;
      font-size: 40px;  /* Plus grand pour compenser */
      line-height: 0.6cm;  /* Ligne plus serrée */
      border-radius: 4px;
      display: flex;
      align-items: stretch;  /* Étirer pour remplir */
      justify-content: center;
      /* Dimensions : 3cm x 0.8cm */
      width: 3cm;
      height: 0.8cm;
      white-space: nowrap;
      overflow: hidden;
      box-sizing: border-box;
      padding: 0;
      margin: 0;
      /* Éliminer tous les espaces */
      position: relative;
      transform: translateY(-2px);  /* Ajuster la position verticale */
    }}
  </style>
  <link href="https://fonts.googleapis.com/css2?family=Libre+Barcode+39&display=swap" rel="stylesheet">
</head>
<body>
  <div id="labels"></div>
  <script>
    const label = document.createElement("div");
    label.className = "label";
    label.innerHTML = `
      <div class="logo"><img src="data:image/jpeg;base64,{logo_base64}" alt="Renault"></div>
      <div class="barcode">*{code_value}*</div>
      <div class="code-number">{code_value} {part_number}</div>
    `;
    document.getElementById("labels").appendChild(label);
  </script>
</body>
</html>"""
    
    filename = f"etiquette_logo_agrandi_{code_value.replace('-', '_')}_{part_number.replace('/', '_')}.html"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Étiquette avec logo agrandi créée: {filename}")
    return filename

def main():
    """Test logo agrandi"""
    print("🎨 Test Logo Agrandi")
    print("=" * 30)
    
    # Vérifier qu'un logo existe
    logo_found = any(os.path.exists(path) for path in ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"])
    if not logo_found:
        print("❌ Logo Renault manquant!")
        return
    
    # Créer l'étiquette
    filename = create_etiquette_logo_agrandi("64004380-0", "1/2")
    
    if filename:
        print(f"📁 Fichier créé: {filename}")
        print("💡 Ouvrez le fichier HTML dans votre navigateur")
        
        # Ouvrir automatiquement
        try:
            os.system(f"start {filename}")
        except:
            print("   (Ouvrez manuellement le fichier)")

if __name__ == "__main__":
    main()
