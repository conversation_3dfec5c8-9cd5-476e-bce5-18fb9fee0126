#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple des modifications du ticket Renault
"""

def test_modifications():
    """Vérifier que les modifications sont bien en place"""
    try:
        print("=== Vérification des modifications du ticket Renault ===")
        
        # Li<PERSON> le fichier app_native.py pour vérifier les modifications
        with open('app_native.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifications
        checks = []
        
        # 1. Vérifier que la taille de police est passée à 70
        if 'nom_font_size = 70' in content:
            checks.append("✅ Taille de police agrandie à 70pt")
        else:
            checks.append("❌ Taille de police non modifiée")
        
        # 2. Vérifier que la position est à 0.7cm
        if 'position_haut_cm = 0.7' in content:
            checks.append("✅ Position modifiée à 0.7cm du haut")
        else:
            checks.append("❌ Position non modifiée")
        
        # 3. Vérifier que les références à description_font_size sont supprimées
        if 'description_font_size = 14' not in content:
            checks.append("✅ Références à description_font_size supprimées")
        else:
            checks.append("❌ Références à description_font_size encore présentes")
        
        # 4. Vérifier que le texte de description est supprimé
        if 'Société Marocaine de Construction Automobile' not in content:
            checks.append("✅ Texte de description supprimé")
        else:
            checks.append("❌ Texte de description encore présent")
        
        # 5. Vérifier les commentaires mis à jour
        if 'SANS description' in content:
            checks.append("✅ Commentaires mis à jour")
        else:
            checks.append("❌ Commentaires non mis à jour")
        
        print("\n📋 Résultats des vérifications:")
        for check in checks:
            print(f"  {check}")
        
        # Compter les succès
        success_count = sum(1 for check in checks if check.startswith("✅"))
        total_count = len(checks)
        
        print(f"\n📊 Score: {success_count}/{total_count} modifications réussies")
        
        if success_count == total_count:
            print("\n🎉 Toutes les modifications ont été appliquées avec succès!")
            print("\n📝 Résumé des changements:")
            print("  • Sous-titre 'Société Marocaine...' SUPPRIMÉ")
            print("  • Taille 'SOMACA' agrandie: 50pt → 70pt")
            print("  • Position 'SOMACA': 0.5cm → 0.7cm du haut")
            return True
        else:
            print(f"\n⚠️  {total_count - success_count} modification(s) manquante(s)")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Vérification des modifications du ticket Renault...")
    success = test_modifications()
    
    if success:
        print("\n✅ Modifications validées!")
        print("🚀 Vous pouvez maintenant relancer l'application pour tester")
    else:
        print("\n❌ Certaines modifications sont manquantes")
