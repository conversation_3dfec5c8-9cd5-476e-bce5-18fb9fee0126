#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de compilation RAPIDE pour SOMACA Native
Évite les problèmes d'encodage de PyInstaller
"""

import PyInstaller.__main__
import os

def build_quick():
    """Compilation rapide sans problèmes d'encodage"""
    
    # Chemin vers l'icône
    icon_path = os.path.join(os.path.dirname(__file__), 'qr_icone.ico')
    
    # Arguments PyInstaller simplifiés
    args = [
        'app_native.py',
        '--onefile',
        '--windowed',
        '--name=SOMACA_Native_Fixed',
        '--icon=' + icon_path,
        '--add-data=web;web',
        '--distpath=dist',
        '--clean',
        '--noconfirm',
        '--hidden-import=webview',
        '--hidden-import=pandas',
        '--hidden-import=qrcode',
        '--hidden-import=barcode',
        '--hidden-import=PIL',
        '--hidden-import=openpyxl',
        '--hidden-import=tkinter',
    ]
    
    print("🚀 Compilation RAPIDE de SOMACA Native...")
    print("⏳ Veuillez patienter...")
    
    try:
        PyInstaller.__main__.run(args)
        
        print("✅ Compilation terminée !")
        exe_path = os.path.join('dist', 'SOMACA_Native_Fixed.exe')
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📊 Taille: {size_mb:.1f} MB")
            print("🎉 Application prête !")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    
    return True

if __name__ == '__main__':
    build_quick()
