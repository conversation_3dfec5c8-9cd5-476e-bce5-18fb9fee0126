# 🏭 SOMACA - Générateur QR & Code à Barre

## 📋 Description
Application native Windows pour générer des codes-barres et QR codes dans des fichiers Excel, avec génération d'étiquettes Renault personnalisées.

**Développé par :** IMAD ELberrouagui  
**Version :** Native Windows (PyWebView)

## 🚀 Utilisation Rapide

### Option 1 : Lancement Direct
Double-cliquez sur `Lancer_SOMACA.bat` pour démarrer l'application automatiquement.

### Option 2 : Lancement Manuel
1. Naviguez vers le dossier `dist/`
2. Double-cliquez sur `SOMACA_Native.exe`

## ✨ Fonctionnalités

### 📊 Génération Excel
- **Codes-barres** : Format Code128, dimensions 4.4cm x 2.2cm
- **QR codes** : Dimensions 2.5cm x 2.5cm, compatibles iOS/Android
- **Texte centré** : Alignement horizontal et vertical
- **Hauteur de ligne** : 2cm automatique
- **Progression temps réel** : Affichage du pourcentage exact

### 🏷️ Étiquettes Renault
- **Formats** : 4cm x 7cm (vertical)
- **Arrière-plans** : Noir ou blanc au choix
- **Logo adaptatif** : Noir sur fond blanc, blanc sur fond noir
- **Zone QR blanche** : Pour une meilleure lisibilité sur fond noir
- **Texte** : 36pt, gras, couleur adaptée au fond
- **Bordure** : Cadre orange Renault

### 🖨️ Options d'Impression
- **Impression directe** : Détection automatique des imprimantes
- **Export d'images** : Sauvegarde en haute résolution
- **Disposition côte à côte** : Code-barre + QR code sur format A4
- **Images encadrées** : Avec bordures pour découpe

## 🔧 Améliorations Récentes

### ⚡ Performances
- **Cache intelligent** : Réutilisation des images générées
- **Traitement par lots** : Optimisation de la génération
- **Progression fluide** : Mise à jour temps réel via pywebview

### 🛑 Annulation
- **Bouton d'annulation** : Disponible pour toutes les opérations
- **Génération Excel** : Arrêt possible à tout moment
- **Tickets Renault** : Annulation pendant la génération
- **Interface réactive** : Réactivation automatique des boutons

### 📱 Interface
- **Titre mis à jour** : "Générateur QR & Code à Barre"
- **Statut détaillé** : "Génération en cours... X%"
- **Messages clairs** : Notifications de succès/erreur
- **Boutons adaptatifs** : Activation selon le contexte

## 💻 Compatibilité

### ✅ Systèmes Supportés
- **Windows 10** (version 1903 ou plus récente)
- **Windows 11** (toutes versions)
- **Edge WebView2** (inclus dans Windows)

### ✅ Aucune Installation Requise
- **Fichier unique** : SOMACA_Native.exe (73.2 MB)
- **Portable** : Fonctionne depuis n'importe quel dossier
- **Autonome** : Toutes les dépendances incluses

## 📁 Structure des Fichiers

```
📦 SOMACA Application
├── 🚀 Lancer_SOMACA.bat          # Script de lancement
├── 📋 README_EXECUTABLE.md       # Ce fichier
├── 📁 dist/
│   └── 💎 SOMACA_Native.exe      # Application principale
├── 📁 web/                       # Interface utilisateur
│   ├── 🌐 index.html
│   ├── 🎨 style.css
│   └── ⚡ script.js
└── 🔧 build_native.py            # Script de compilation
```

## 🎯 Guide d'Utilisation

### 1️⃣ Sélection des Fichiers
1. **Fichier Excel source** : Cliquez sur "Parcourir" pour sélectionner votre fichier
2. **Dossier de destination** : Choisissez où sauvegarder les résultats

### 2️⃣ Options de Génération
- ☑️ **Codes-barres** : Génère des codes Code128
- ☑️ **QR codes** : Génère des QR codes complets
- 🚀 **Générer** : Lance la création du fichier Excel

### 3️⃣ Étiquettes Renault
1. Générez d'abord un fichier Excel avec codes
2. Cliquez sur "🏷️ Étiquettes Renault"
3. Choisissez les couleurs de fond (noir/blanc)
4. Les étiquettes sont créées automatiquement

### 4️⃣ Impression
1. Cliquez sur "🖨️ Options d'Impression"
2. Choisissez entre impression directe ou export d'images
3. Sélectionnez les types de codes à imprimer

## 🛠️ Dépannage

### ❌ L'application ne démarre pas
- Vérifiez que vous êtes sur Windows 10/11
- Assurez-vous qu'Edge WebView2 est installé
- Exécutez en tant qu'administrateur si nécessaire

### ❌ Erreur de génération
- Vérifiez que le fichier Excel n'est pas ouvert dans Excel
- Assurez-vous d'avoir les droits d'écriture dans le dossier de destination
- Vérifiez que le fichier source contient des données

### ❌ QR codes non lisibles sur iOS
- Les QR codes sont maintenant compatibles iOS/Android
- Format multi-lignes : "Nom=Valeur" pour chaque champ

## 📞 Support

Pour toute question ou problème :
- **Développeur** : IMAD ELberrouagui
- **Société** : SOMACA (Société Marocaine de Construction Automobile)
- **Groupe** : Renault

## 🔄 Historique des Versions

### Version Actuelle (2025-01-11)
- ✅ Titre application mis à jour
- ✅ Progression temps réel corrigée
- ✅ Bouton d'annulation pour tickets Renault
- ✅ Performances optimisées
- ✅ Cache intelligent ajouté
- ✅ Interface pywebview native

---

**© 2024-2025 SOMACA - Tous droits réservés**
