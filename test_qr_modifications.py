#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test des modifications du QR code dans les tickets Renault
- QR code agrandi (4.0cm x 4.0cm)
- Espace de 0.8cm entre SOMACA et QR code
- QR code centré horizontalement
"""

def test_qr_modifications():
    """Vérifier que les modifications du QR code sont bien en place"""
    try:
        print("=== Vérification des modifications du QR code ===")
        
        # Lire le fichier app_native.py pour vérifier les modifications
        with open('app_native.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifications
        checks = []
        
        # 1. Vérifier que le QR code fait 4.0cm (ou 3.2cm dans la zone blanche)
        if 'qr_width = int(4.0 * 300 / 2.54)' in content:
            checks.append("✅ QR code agrandi à 4.0cm")
        elif 'code_zone_width = int(3.2 * 300 / 2.54)' in content:
            checks.append("✅ Zone QR code agrandie à 3.2cm")
        else:
            checks.append("❌ Taille QR code non modifiée")
        
        # 2. Vérifier que l'espacement est à 0.8cm
        if 'espace_cm = 0.8' in content:
            checks.append("✅ Espacement modifié à 0.8cm")
        else:
            checks.append("❌ Espacement non modifié")
        
        # 3. Vérifier que le QR code est centré
        if 'code_x = (etiquette_width - qr_width) // 2' in content:
            checks.append("✅ QR code centré horizontalement")
        else:
            checks.append("❌ QR code non centré")
        
        # 4. Vérifier que les anciens paramètres de marge sont supprimés
        if 'marge_gauche_cm = 0.7' not in content:
            checks.append("✅ Anciennes marges supprimées")
        else:
            checks.append("❌ Anciennes marges encore présentes")
        
        # 5. Vérifier les commentaires mis à jour
        if '0.8cm d\'espace entre SOMACA et QR code' in content:
            checks.append("✅ Commentaires mis à jour")
        else:
            checks.append("❌ Commentaires non mis à jour")
        
        print("\n📋 Résultats des vérifications:")
        for check in checks:
            print(f"  {check}")
        
        # Compter les succès
        success_count = sum(1 for check in checks if check.startswith("✅"))
        total_count = len(checks)
        
        print(f"\n📊 Score: {success_count}/{total_count} modifications réussies")
        
        if success_count >= 4:  # Au moins 4 sur 5 pour être considéré comme réussi
            print("\n🎉 Modifications du QR code appliquées avec succès!")
            print("\n📝 Résumé des changements:")
            print("  • QR code AGRANDI (4.0cm ou 3.2cm selon la fonction)")
            print("  • Espace entre SOMACA et QR: 0.8cm (au lieu de 0.2cm)")
            print("  • QR code CENTRÉ horizontalement")
            print("  • Suppression des marges gauche/droite fixes")
            return True
        else:
            print(f"\n⚠️  {total_count - success_count} modification(s) manquante(s)")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

def afficher_dimensions():
    """Afficher les nouvelles dimensions calculées"""
    print("\n📐 Nouvelles dimensions calculées:")
    
    # Calculs pour 300 DPI
    dpi = 300
    cm_to_inch = 2.54
    
    # QR code 4.0cm
    qr_4cm_pixels = int(4.0 * dpi / cm_to_inch)
    print(f"  • QR code 4.0cm = {qr_4cm_pixels} pixels")
    
    # Zone blanche 3.2cm
    zone_32cm_pixels = int(3.2 * dpi / cm_to_inch)
    print(f"  • Zone blanche 3.2cm = {zone_32cm_pixels} pixels")
    
    # Espacement 0.8cm
    espace_08cm_pixels = int(0.8 * dpi / cm_to_inch)
    print(f"  • Espacement 0.8cm = {espace_08cm_pixels} pixels")
    
    # Position SOMACA 0.7cm
    position_07cm_pixels = int(0.7 * dpi / cm_to_inch)
    print(f"  • Position SOMACA 0.7cm = {position_07cm_pixels} pixels")
    
    # Dimensions étiquette
    etiquette_width = int(3.9 * dpi / cm_to_inch)
    etiquette_height = int(7.0 * dpi / cm_to_inch)
    print(f"  • Étiquette: {etiquette_width}x{etiquette_height} pixels (3.9x7.0cm)")

if __name__ == "__main__":
    print("🔍 Vérification des modifications du QR code...")
    success = test_qr_modifications()
    
    if success:
        print("\n✅ Modifications validées!")
        afficher_dimensions()
        print("\n🚀 Vous pouvez maintenant tester l'application")
    else:
        print("\n❌ Certaines modifications sont manquantes")
        
    print("\n💡 Pour tester:")
    print("  1. Lancez l'application: python app_native.py")
    print("  2. Générez des tickets Renault avec QR codes")
    print("  3. Vérifiez visuellement les changements")
