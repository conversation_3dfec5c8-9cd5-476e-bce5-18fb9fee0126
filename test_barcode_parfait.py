#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour code-barres PARFAIT : barres complètes + UN SEUL numéro en bas
"""

import os
from PIL import Image, ImageDraw, ImageFont
import barcode
from barcode.writer import ImageWriter
import io

def create_perfect_barcode_frame(barcode_number="64004380-0", part_number="1/2"):
    """Créer un cadre avec code-barres PARFAIT (barres complètes) + UN SEUL numéro en bas"""
    try:
        # Dimensions du cadre : 6cm x 3cm à 300 DPI
        frame_width = int(6.0 * 300 / 2.54)  # 708 pixels
        frame_height = int(3.0 * 300 / 2.54)  # 354 pixels
        
        print(f"🎨 Création cadre PARFAIT pour: {barcode_number} {part_number}")
        
        # Créer l'image du cadre avec fond noir
        frame_img = Image.new('RGB', (frame_width, frame_height), '#2a2a2a')
        draw = ImageDraw.Draw(frame_img)
        
        # Ajouter les rivets dorés dans les coins
        rivet_color = '#FFD700'
        rivet_radius = 8
        margin = 15
        
        # Dessiner les 4 rivets
        positions = [
            (margin, margin),  # Haut gauche
            (frame_width-margin, margin),  # Haut droite
            (margin, frame_height-margin),  # Bas gauche
            (frame_width-margin, frame_height-margin)  # Bas droite
        ]
        
        for x, y in positions:
            draw.ellipse([x-rivet_radius, y-rivet_radius, 
                         x+rivet_radius, y+rivet_radius], fill=rivet_color)
        
        # Charger et positionner le logo Renault
        logo_path = "renault-logo.png"
        if os.path.exists(logo_path):
            try:
                logo = Image.open(logo_path)
                logo_width = 100
                logo_height = 50
                logo = logo.resize((logo_width, logo_height), Image.Resampling.LANCZOS)
                
                # Logo centré en haut
                logo_x = (frame_width - logo_width) // 2
                logo_y = 25
                frame_img.paste(logo, (logo_x, logo_y), logo if logo.mode == 'RGBA' else None)
                print("✅ Logo Renault ajouté")
            except Exception as e:
                print(f"⚠️ Erreur logo: {e}")
        
        # Générer le code-barres avec configuration spéciale
        code128 = barcode.get_barcode_class('code128')
        
        # Configuration pour avoir des barres propres
        writer = ImageWriter()
        writer.set_options({
            'module_width': 0.2,    # Largeur des barres
            'module_height': 15.0,  # Hauteur des barres
            'quiet_zone': 6.5,      # Zone de silence
            'font_size': 0,         # Taille de police à 0 pour supprimer le texte
            'text_distance': 0,     # Distance du texte à 0
            'background': 'white',  # Fond blanc
            'foreground': 'black',  # Barres noires
            'write_text': False,    # Pas de texte
        })
        
        barcode_instance = code128(barcode_number, writer=writer)
        
        barcode_buffer = io.BytesIO()
        barcode_instance.write(barcode_buffer)
        barcode_buffer.seek(0)
        barcode_img_original = Image.open(barcode_buffer)
        
        print(f"📏 Taille code-barres original: {barcode_img_original.size}")
        
        # Analyser l'image pour trouver où commencent et finissent les barres
        img_array = list(barcode_img_original.getdata())
        width, height = barcode_img_original.size
        
        # Trouver la première ligne qui contient des barres noires
        first_bar_line = 0
        last_bar_line = height - 1
        
        for y in range(height):
            line_has_black = False
            for x in range(width):
                pixel_index = y * width + x
                if pixel_index < len(img_array):
                    pixel = img_array[pixel_index]
                    # Vérifier si le pixel est noir (ou proche du noir)
                    if isinstance(pixel, tuple):
                        r, g, b = pixel[:3]
                        if r < 128 and g < 128 and b < 128:  # Pixel sombre
                            line_has_black = True
                            break
                    elif pixel < 128:  # Image en niveaux de gris
                        line_has_black = True
                        break
            
            if line_has_black:
                if first_bar_line == 0:
                    first_bar_line = y
                last_bar_line = y
        
        print(f"📊 Barres détectées de la ligne {first_bar_line} à {last_bar_line}")
        
        # Extraire seulement la zone des barres avec une petite marge
        margin_bars = 5
        crop_top = max(0, first_bar_line - margin_bars)
        crop_bottom = min(height, last_bar_line + margin_bars)
        
        barcode_img_clean = barcode_img_original.crop((0, crop_top, width, crop_bottom))
        print(f"📏 Taille après extraction propre: {barcode_img_clean.size}")
        
        # Redimensionner le code-barres propre
        barcode_width = 500  # Large pour bien remplir l'espace
        barcode_height = 80   # Hauteur appropriée
        barcode_img_resized = barcode_img_clean.resize((barcode_width, barcode_height), Image.Resampling.LANCZOS)
        
        # Positionner le code-barres au centre (sous le logo)
        barcode_x = (frame_width - barcode_width) // 2
        barcode_y = 100  # Sous le logo
        frame_img.paste(barcode_img_resized, (barcode_x, barcode_y))
        print(f"📊 Code-barres PARFAIT positionné à ({barcode_x}, {barcode_y})")
        
        # Ajouter UN SEUL numéro en bas (séparé du code-barres)
        try:
            font_large = ImageFont.truetype("arial.ttf", 32)  # Plus gros
        except:
            font_large = ImageFont.load_default()
        
        # Le numéro affiché en bas, en blanc sur fond noir
        display_text = f"{barcode_number} {part_number}"
        bbox = draw.textbbox((0, 0), display_text, font=font_large)
        text_width = bbox[2] - bbox[0]
        text_x = (frame_width - text_width) // 2
        text_y = frame_height - 80  # En bas avec marge
        draw.text((text_x, text_y), display_text, fill='white', font=font_large)
        print(f"🔤 UN SEUL texte '{display_text}' positionné à ({text_x}, {text_y})")
        
        # Sauvegarder
        safe_filename = barcode_number.replace('-', '_').replace('/', '_')
        save_path = f"cadre_parfait_{safe_filename}_{part_number.replace('/', '_')}.png"
        frame_img.save(save_path, 'PNG', dpi=(300, 300))
        print(f"💾 Sauvé: {save_path}")
        
        return save_path
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Test du code-barres PARFAIT"""
    print("🧪 Test Code-barres PARFAIT (barres complètes + UN SEUL texte)")
    print("=" * 70)
    
    if not os.path.exists("renault-logo.png"):
        print("❌ Logo Renault manquant!")
        return
    
    # Test avec le numéro de votre exemple
    result = create_perfect_barcode_frame("64004380-0", "1/2")
    
    if result:
        print(f"\n✅ Test réussi!")
        print(f"📁 Fichier créé: {result}")
        print("\n💡 Maintenant vous devriez voir:")
        print("   ✓ Code-barres avec barres COMPLÈTES et propres")
        print("   ✓ UN SEUL numéro en bas: '64004380-0 1/2'")
        print("   ✓ Logo Renault au-dessus")
        print("   ✓ Rivets dorés dans les coins")
        print("   ✓ Pas d'effet de découpage")
        
        # Ouvrir le fichier pour vérification
        try:
            os.system(f"start {result}")
        except:
            print("   (Ouvrez manuellement le fichier pour vérifier)")
    else:
        print("❌ Échec du test")

if __name__ == "__main__":
    main()
