#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Solution finale : Code-barres sans texte intégré + UN SEUL numéro en bas
"""

import os
from PIL import Image, ImageDraw, ImageFont
import barcode
from barcode.writer import ImageWriter
import io

def generate_clean_barcode_image(data):
    """Génère un code-barres SANS texte intégré (seulement les barres)"""
    try:
        # Obtenir la classe code128
        code128 = barcode.get_barcode_class('code128')
        
        # Configuration du writer pour supprimer le texte
        writer = ImageWriter()
        writer.set_options({
            'write_text': False,    # Pas de texte
            'text_distance': 0,     # Pas d'espace pour le texte
            'quiet_zone': 2,        # Zone de silence minimale
            'module_height': 15,    # Hauteur des barres
            'module_width': 0.2,    # Largeur des barres
        })
        
        # Créer le code-barres
        barcode_instance = code128(data, writer=writer)
        
        # Générer l'image
        buffer = io.BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)
        
        # Charger l'image
        barcode_img = Image.open(buffer)
        
        print(f"📊 Code-barres généré sans texte: {barcode_img.size}")
        return barcode_img
        
    except Exception as e:
        print(f"❌ Erreur génération code-barres propre: {e}")
        return None

def create_final_solution_frame(barcode_number="64004380-0", part_number="1/2"):
    """Solution finale : cadre avec code-barres propre + UN SEUL numéro"""
    try:
        # Dimensions du cadre : 6cm x 3cm à 300 DPI
        frame_width = int(6.0 * 300 / 2.54)  # 708 pixels
        frame_height = int(3.0 * 300 / 2.54)  # 354 pixels
        
        print(f"🎯 SOLUTION FINALE pour: {barcode_number} {part_number}")
        
        # Créer l'image du cadre avec fond noir
        frame_img = Image.new('RGB', (frame_width, frame_height), '#2a2a2a')
        draw = ImageDraw.Draw(frame_img)
        
        # Ajouter les rivets dorés dans les coins
        rivet_color = '#FFD700'
        rivet_radius = 8
        margin = 15
        
        # Dessiner les 4 rivets
        positions = [
            (margin, margin),  # Haut gauche
            (frame_width-margin, margin),  # Haut droite
            (margin, frame_height-margin),  # Bas gauche
            (frame_width-margin, frame_height-margin)  # Bas droite
        ]
        
        for x, y in positions:
            draw.ellipse([x-rivet_radius, y-rivet_radius, 
                         x+rivet_radius, y+rivet_radius], fill=rivet_color)
        
        # Charger et positionner le logo Renault
        logo_path = "renault-logo.png"
        if os.path.exists(logo_path):
            try:
                logo = Image.open(logo_path)
                logo_width = 100
                logo_height = 50
                logo = logo.resize((logo_width, logo_height), Image.Resampling.LANCZOS)
                
                # Logo centré en haut
                logo_x = (frame_width - logo_width) // 2
                logo_y = 25
                frame_img.paste(logo, (logo_x, logo_y), logo if logo.mode == 'RGBA' else None)
                print("✅ Logo Renault ajouté")
            except Exception as e:
                print(f"⚠️ Erreur logo: {e}")
        
        # Générer le code-barres PROPRE (sans texte)
        barcode_img = generate_clean_barcode_image(barcode_number)
        
        if barcode_img:
            # Redimensionner le code-barres
            barcode_width = 500
            barcode_height = 80
            barcode_img_resized = barcode_img.resize((barcode_width, barcode_height), Image.Resampling.LANCZOS)
            
            # Positionner le code-barres au centre (sous le logo)
            barcode_x = (frame_width - barcode_width) // 2
            barcode_y = 100
            frame_img.paste(barcode_img_resized, (barcode_x, barcode_y))
            print(f"📊 Code-barres PROPRE positionné")
        
        # Ajouter UN SEUL numéro en bas
        try:
            font_large = ImageFont.truetype("arial.ttf", 32)
        except:
            font_large = ImageFont.load_default()
        
        # Le numéro affiché en bas, en blanc sur fond noir
        display_text = f"{barcode_number} {part_number}"
        bbox = draw.textbbox((0, 0), display_text, font=font_large)
        text_width = bbox[2] - bbox[0]
        text_x = (frame_width - text_width) // 2
        text_y = frame_height - 80
        draw.text((text_x, text_y), display_text, fill='white', font=font_large)
        print(f"🔤 UN SEUL texte ajouté: '{display_text}'")
        
        # Sauvegarder
        safe_filename = barcode_number.replace('-', '_').replace('/', '_')
        save_path = f"solution_finale_{safe_filename}_{part_number.replace('/', '_')}.png"
        frame_img.save(save_path, 'PNG', dpi=(300, 300))
        print(f"💾 SOLUTION FINALE sauvée: {save_path}")
        
        return save_path
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_different_numbers():
    """Tester avec différents numéros"""
    test_cases = [
        ("64004380-0", "1/2"),
        ("12345678-9", "2/3"),
        ("ABCD1234", "1/1")
    ]
    
    results = []
    for number, part in test_cases:
        print(f"\n{'='*50}")
        result = create_final_solution_frame(number, part)
        if result:
            results.append(result)
    
    return results

def main():
    """Test de la solution finale"""
    print("🎯 SOLUTION FINALE - Code-barres propre + UN SEUL numéro")
    print("=" * 70)
    
    if not os.path.exists("renault-logo.png"):
        print("❌ Logo Renault manquant!")
        return
    
    # Tester avec différents numéros
    results = test_different_numbers()
    
    if results:
        print(f"\n🎉 SOLUTION FINALE réussie!")
        print(f"📁 {len(results)} fichiers créés:")
        for result in results:
            print(f"   - {result}")
        
        print("\n💡 Chaque image devrait avoir:")
        print("   ✓ Code-barres avec SEULEMENT les barres (pas de texte intégré)")
        print("   ✓ UN SEUL numéro en bas en texte blanc")
        print("   ✓ Logo Renault au-dessus")
        print("   ✓ Rivets dorés dans les coins")
        print("   ✓ Dimensions exactes: 6cm x 3cm")
        
        # Ouvrir le premier fichier pour vérification
        try:
            os.system(f"start {results[0]}")
        except:
            print("   (Ouvrez manuellement les fichiers pour vérifier)")
    else:
        print("❌ Échec de la solution finale")

if __name__ == "__main__":
    main()
