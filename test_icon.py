#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour vérifier les icônes de l'application SOMACA
Développé par: IMAD ELberrouagui
"""

import os
from PIL import Image

def test_icons():
    """Tester la présence et la validité des icônes"""
    
    print("=" * 60)
    print("🏭 SOMACA - Test des Icônes")
    print("🔍 Vérification des fichiers d'icônes")
    print("👨‍💻 Développé par: IMAD ELberrouagui")
    print("=" * 60)
    
    # Fichiers à vérifier
    files_to_check = [
        ('exel.png', 'Icône PNG principale'),
        ('exel.ico', 'Icône ICO Windows'),
        ('web/exel.png', 'Icône web (favicon)'),
    ]
    
    all_good = True
    
    for file_path, description in files_to_check:
        print(f"\n🔍 Vérification: {description}")
        print(f"📁 Fichier: {file_path}")
        
        if os.path.exists(file_path):
            try:
                # Vérifier la taille du fichier
                size_bytes = os.path.getsize(file_path)
                size_kb = size_bytes / 1024
                print(f"✅ Fichier trouvé ({size_kb:.1f} KB)")
                
                # Vérifier que c'est une image valide
                if file_path.endswith('.png'):
                    img = Image.open(file_path)
                    print(f"📐 Dimensions: {img.width}x{img.height}")
                    print(f"🎨 Mode: {img.mode}")
                    
                elif file_path.endswith('.ico'):
                    # Pour les .ico, on peut juste vérifier qu'ils s'ouvrent
                    img = Image.open(file_path)
                    print(f"📐 Dimensions: {img.width}x{img.height}")
                    print(f"🎨 Format: ICO multi-résolutions")
                
                print(f"✅ {description} - OK")
                
            except Exception as e:
                print(f"❌ Erreur lors de la lecture: {e}")
                all_good = False
        else:
            print(f"❌ Fichier non trouvé: {file_path}")
            all_good = False
    
    # Vérifier la configuration HTML
    print(f"\n🌐 Vérification de la configuration HTML...")
    html_file = 'web/index.html'
    
    if os.path.exists(html_file):
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'exel.png' in content:
            print("✅ Référence à exel.png trouvée dans index.html")
        else:
            print("❌ Référence à exel.png non trouvée dans index.html")
            all_good = False
    else:
        print("❌ Fichier index.html non trouvé")
        all_good = False
    
    # Résumé final
    print("\n" + "=" * 60)
    if all_good:
        print("🎉 TOUS LES TESTS RÉUSSIS !")
        print("✅ L'icône exel.png est correctement configurée")
        print("🚀 L'application est prête à être compilée")
        print("💡 L'icône sera visible dans:")
        print("   • L'onglet du navigateur")
        print("   • La barre des tâches Windows")
        print("   • Le bureau (raccourci)")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("🔧 Vérifiez les fichiers manquants ou corrompus")
    
    print("=" * 60)
    return all_good

if __name__ == '__main__':
    test_icons()
