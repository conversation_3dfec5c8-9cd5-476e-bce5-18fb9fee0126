#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST FINAL - Vérification complète des codes-barres
"""

import pandas as pd
import barcode
from barcode.writer import ImageWriter
import io
from openpyxl import Workbook
from openpyxl.drawing.image import Image as OpenpyxlImage
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
import os

def clean_text_for_barcode(text):
    """Nettoyer le texte pour le rendre compatible avec Code128"""
    if not text:
        return ""
    
    # Convertir en string si ce n'est pas déjà le cas
    text = str(text)

    # Remplacer les caractères accentués par leurs équivalents ASCII
    replacements = {
        'à': 'a', 'á': 'a', 'â': 'a', 'ã': 'a', 'ä': 'a', 'å': 'a',
        'è': 'e', 'é': 'e', 'ê': 'e', 'ë': 'e',
        'ì': 'i', 'í': 'i', 'î': 'i', 'ï': 'i',
        'ò': 'o', 'ó': 'o', 'ô': 'o', 'õ': 'o', 'ö': 'o',
        'ù': 'u', 'ú': 'u', 'û': 'u', 'ü': 'u',
        'ý': 'y', 'ÿ': 'y',
        'ç': 'c', 'ñ': 'n',
        'À': 'A', 'Á': 'A', 'Â': 'A', 'Ã': 'A', 'Ä': 'A', 'Å': 'A',
        'È': 'E', 'É': 'E', 'Ê': 'E', 'Ë': 'E',
        'Ì': 'I', 'Í': 'I', 'Î': 'I', 'Ï': 'I',
        'Ò': 'O', 'Ó': 'O', 'Ô': 'O', 'Õ': 'O', 'Ö': 'O',
        'Ù': 'U', 'Ú': 'U', 'Û': 'U', 'Ü': 'U',
        'Ý': 'Y', 'Ÿ': 'Y',
        'Ç': 'C', 'Ñ': 'N'
    }

    cleaned_text = str(text)
    for accented, ascii_char in replacements.items():
        cleaned_text = cleaned_text.replace(accented, ascii_char)

    # Supprimer tous les caractères non-ASCII restants
    cleaned_text = ''.join(char for char in cleaned_text if ord(char) < 128)

    return cleaned_text

def is_ascii_compatible(text):
    """Vérifier si le texte ne contient que des caractères compatibles avec Code128"""
    try:
        text.encode('ascii')
        return True
    except UnicodeEncodeError:
        return False

def generate_barcode_image(data):
    """Génère une image de code-barre"""
    try:
        print(f"🔍 Génération code-barre pour: '{data}'")
        
        # NETTOYER D'ABORD les données pour les rendre compatibles
        cleaned_data = clean_text_for_barcode(str(data))
        print(f"🧹 Données nettoyées: '{cleaned_data}'")
        
        # Vérifier si les données sont vides
        if not cleaned_data or cleaned_data.strip() == "":
            print(f"❌ Données vides après nettoyage")
            return None
        
        # Vérifier si les données nettoyées sont compatibles
        if not is_ascii_compatible(cleaned_data):
            print(f"❌ Caractères non supportés après nettoyage")
            return None

        print(f"✅ Données compatibles ASCII")

        # Obtenir la classe code128
        code128_class = barcode.get_barcode_class('code128')
        barcode_instance = code128_class(cleaned_data, writer=ImageWriter())

        # Générer l'image en mémoire
        buffer = io.BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)

        # Créer l'objet image pour Excel
        img = OpenpyxlImage(buffer)
        img.width = 85
        img.height = 35

        print(f"✅ Code-barre généré avec succès !")
        return img
    except Exception as e:
        print(f"❌ Erreur génération code-barre: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_complete_generation():
    """Test complet avec un fichier Excel réel"""
    print("🚀 TEST COMPLET GÉNÉRATION CODES-BARRES")
    print("=" * 50)
    
    # Créer des données de test
    test_data = [
        ["PRD001", "Smartphone", "Téléphone portable"],
        ["PRD002", "Ordinateur", "PC de bureau"],
        ["PRD003", "Montre Connectée", "Apple Watch"],
        ["PRD004", "Clavier Mécanique", "Gaming"],
        ["PRD005", "Écran 4K", "Monitor LG"]
    ]
    
    headers = ["SKU", "Nom", "Description"]
    
    # Créer le workbook
    workbook = Workbook()
    worksheet = workbook.active
    worksheet.title = "Test Codes-Barres"
    
    # Styles
    header_font = Font(name='Segoe UI', size=11, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='003366', end_color='003366', fill_type='solid')
    header_alignment = Alignment(horizontal='center', vertical='center')
    
    # Bordures
    thin_border = Side(border_style="thin", color="CCCCCC")
    header_border = Border(left=thin_border, right=thin_border, top=thin_border, bottom=thin_border)
    
    # Ajouter colonne code-barre
    all_headers = headers + ["Code-Barres"]
    barcode_col = len(all_headers)
    
    # Écrire les en-têtes
    for col_num, header in enumerate(all_headers, 1):
        cell = worksheet.cell(row=1, column=col_num, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = header_border
    
    print(f"📋 En-têtes écrits: {all_headers}")
    
    # Traitement des données
    success_count = 0
    for row_index, row_data in enumerate(test_data):
        row_num = row_index + 2
        
        print(f"\n📝 Ligne {row_index + 1}: {row_data}")
        
        # Écrire les données originales
        for col_index, value in enumerate(row_data):
            cell = worksheet.cell(row=row_num, column=col_index + 1, value=value)
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = Border(left=thin_border, right=thin_border, top=thin_border, bottom=thin_border)
        
        # Générer le code-barre
        col1 = row_data[0] if len(row_data) > 0 else ""
        col2 = row_data[1] if len(row_data) > 1 else ""
        barcode_data = f"{col1}-{col2}"
        
        print(f"🔍 Données code-barre: '{barcode_data}'")
        
        # Cellule code-barre
        barcode_cell = worksheet.cell(row=row_num, column=barcode_col, value="")
        barcode_cell.border = Border(left=thin_border, right=thin_border, top=thin_border, bottom=thin_border)
        barcode_cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # Générer l'image du code-barre
        barcode_img = generate_barcode_image(barcode_data)
        
        if barcode_img:
            # Positionner l'image
            col_letter = chr(64 + barcode_col)
            barcode_img.anchor = f"{col_letter}{row_num}"
            barcode_img.left = 12
            barcode_img.top = 10
            
            worksheet.add_image(barcode_img)
            print(f"✅ Code-barre ajouté en {col_letter}{row_num}")
            success_count += 1
        else:
            barcode_cell.value = "ERREUR"
            print(f"❌ Échec génération code-barre")
    
    # Ajuster les largeurs de colonnes
    for col in range(1, len(headers) + 1):
        worksheet.column_dimensions[chr(64 + col)].width = 20
    
    # Colonne code-barre plus large
    worksheet.column_dimensions[chr(64 + barcode_col)].width = 25
    
    # Hauteur des lignes
    for row in range(2, len(test_data) + 2):
        worksheet.row_dimensions[row].height = 56.7  # 2cm
    
    # Sauvegarder
    output_file = "test_final_codes_barres.xlsx"
    workbook.save(output_file)
    
    print("\n" + "=" * 50)
    print(f"🎯 RÉSULTAT FINAL:")
    print(f"✅ Codes-barres générés: {success_count}/{len(test_data)}")
    print(f"📁 Fichier sauvé: {output_file}")
    
    if success_count == len(test_data):
        print("🎉 TOUS LES CODES-BARRES ONT ÉTÉ GÉNÉRÉS AVEC SUCCÈS !")
        return True
    else:
        print("❌ CERTAINS CODES-BARRES ONT ÉCHOUÉ")
        return False

if __name__ == "__main__":
    test_complete_generation()
