# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['app_native.py'],
    pathex=[],
    binaries=[],
    datas=[('web', 'web')],
    hiddenimports=['webview', 'pandas', 'qrcode', 'barcode', 'PIL', 'openpyxl', 'tkinter'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='SOMACA_Native_Fixed',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['C:\\Users\\<USER>\\Documents\\augment-projects\\qr code exel\\somaca_web_app\\qr_icone.ico'],
)
