#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour vérifier que les corrections de génération multiple fonctionnent
"""

import os
import sys
import tempfile
import pandas as pd
from app_native import SomacaBarcodeGenerator

def create_test_excel(filename):
    """Créer un fichier Excel de test"""
    data = {
        'Colonne1': ['Test1', 'Test2', 'Test3'],
        'Colonne2': ['A', 'B', 'C'],
        'Colonne3': ['Data1', 'Data2', 'Data3']
    }
    df = pd.DataFrame(data)
    df.to_excel(filename, index=False)
    print(f"✅ Fichier de test créé: {filename}")

def test_multiple_generations():
    """Tester plusieurs générations successives"""
    print("🧪 Test de génération multiple...")
    
    # Créer un générateur
    generator = SomacaBarcodeGenerator()
    
    # Créer un dossier temporaire
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Dossier temporaire: {temp_dir}")
        
        # Créer un fichier Excel de test
        test_file = os.path.join(temp_dir, "test_data.xlsx")
        create_test_excel(test_file)
        
        # Définir les chemins
        generator.set_input_file(test_file)
        generator.set_output_folder(temp_dir)
        
        # Test 1: Première génération
        print("\n🔄 Test 1: Première génération...")
        result1 = generator.generate_codes(generate_barcodes=True, generate_qr=True)
        
        if result1["success"]:
            print("✅ Première génération réussie")
        else:
            print(f"❌ Première génération échouée: {result1['message']}")
            return False
        
        # Test 2: Deuxième génération (devrait déclencher la modal de conflit)
        print("\n🔄 Test 2: Deuxième génération (conflit attendu)...")
        result2 = generator.generate_codes(generate_barcodes=True, generate_qr=True)
        
        if result2.get("file_exists"):
            print("✅ Conflit de fichier détecté correctement")
        else:
            print(f"❌ Conflit non détecté: {result2}")
        
        # Test 3: Génération avec nouveau fichier
        print("\n🔄 Test 3: Nouveau fichier...")
        test_file2 = os.path.join(temp_dir, "test_data2.xlsx")
        create_test_excel(test_file2)
        
        generator.set_input_file(test_file2)
        result3 = generator.generate_codes(generate_barcodes=True, generate_qr=True)
        
        if result3["success"]:
            print("✅ Génération avec nouveau fichier réussie")
        else:
            print(f"❌ Génération avec nouveau fichier échouée: {result3['message']}")
            return False
        
        # Test 4: Vérifier la réinitialisation
        print("\n🔄 Test 4: Test de réinitialisation...")
        generator.reset_application_state()
        print("✅ Réinitialisation effectuée")
        
        # Test 5: Génération après réinitialisation
        print("\n🔄 Test 5: Génération après réinitialisation...")
        generator.set_input_file(test_file)
        generator.set_output_folder(temp_dir)
        
        # Supprimer le fichier de sortie précédent pour éviter le conflit
        output_file = os.path.join(temp_dir, "test_data_avec_codes.xlsx")
        if os.path.exists(output_file):
            os.remove(output_file)
        
        result5 = generator.generate_codes(generate_barcodes=True, generate_qr=True)
        
        if result5["success"]:
            print("✅ Génération après réinitialisation réussie")
        else:
            print(f"❌ Génération après réinitialisation échouée: {result5['message']}")
            return False
    
    print("\n🎉 Tous les tests sont passés avec succès!")
    return True

if __name__ == "__main__":
    try:
        success = test_multiple_generations()
        if success:
            print("\n✅ SUCCÈS: Les corrections fonctionnent correctement")
            sys.exit(0)
        else:
            print("\n❌ ÉCHEC: Des problèmes persistent")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 ERREUR CRITIQUE: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
