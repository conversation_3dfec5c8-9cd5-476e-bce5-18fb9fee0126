#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test code-barres avec write_text=False - SEULEMENT les barres
"""

import os
import base64
import barcode
from barcode.writer import ImageWriter
import qrcode
from PIL import Image
import io

def get_logo_base64():
    """Convertit le logo Renault en base64"""
    try:
        logo_paths = ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"]
        for logo_path in logo_paths:
            if os.path.exists(logo_path):
                with open(logo_path, "rb") as f:
                    logo_data = f.read()
                print(f"✅ Logo trouvé: {logo_path}")
                return base64.b64encode(logo_data).decode('utf-8')
        return ""
    except Exception as e:
        print(f"⚠️ Erreur: {e}")
        return ""

def generate_barcode_only_bars(code_value):
    """Génère code-barres avec write_text=False - SEULEMENT les barres"""
    try:
        print(f"🔧 Génération code-barres pour: {code_value}")
        
        # G<PERSON>érer code-barres Code128
        code128 = barcode.get_barcode_class('code128')
        
        # Configuration STRICTE : write_text=False
        writer = ImageWriter()
        writer.set_options({
            'write_text': False,    # ✅ AUCUN texte dans l'image
            'text_distance': 0,     # Pas d'espace pour texte
            'quiet_zone': 1,        # Zone minimale
            'module_width': 0.2,    # Largeur barres
            'module_height': 15,    # Hauteur barres
            'background': 'white',  
            'foreground': 'black',  
        })
        
        barcode_instance = code128(code_value, writer=writer)
        
        # Générer l'image
        buffer = io.BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)
        
        barcode_img = Image.open(buffer)
        print(f"📊 Code-barres généré: {barcode_img.size} (seulement barres)")
        
        # Convertir en base64
        img_buffer = io.BytesIO()
        barcode_img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        barcode_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
        return barcode_base64
        
    except Exception as e:
        print(f"❌ Erreur code-barres: {e}")
        return ""

def generate_qr_code(code_value):
    """Génère QR code"""
    try:
        print(f"📱 Génération QR code pour: {code_value}")
        
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=8,
            border=2,
        )
        qr.add_data(code_value)
        qr.make(fit=True)
        
        qr_img = qr.make_image(fill_color="black", back_color="white")
        print(f"📱 QR code généré: {qr_img.size}")
        
        # Convertir en base64
        img_buffer = io.BytesIO()
        qr_img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        qr_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
        return qr_base64
        
    except Exception as e:
        print(f"❌ Erreur QR code: {e}")
        return ""

def create_etiquette_sans_texte(code_value="64004380-0", part_number="1/2", code_type="barcode"):
    """Créer étiquette avec code sans texte intégré"""
    
    logo_base64 = get_logo_base64()
    
    if code_type == "barcode":
        code_base64 = generate_barcode_only_bars(code_value)
        code_title = "Code-barres (barres seulement)"
    else:
        code_base64 = generate_qr_code(code_value)
        code_title = "QR Code"
    
    html_content = f"""<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Étiquette Renault - {code_title}</title>
  <style>
    body {{
      margin: 0;
      padding: 20px;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
    }}
    .label {{
      width: 6cm;
      height: 3cm;
      background-color: #000;
      border-radius: 8px;
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      font-family: Arial, sans-serif;
      margin: 0 auto;
      box-sizing: border-box;
      overflow: hidden;
      padding: 4px;
    }}
    .logo img {{
      height: 50px;
    }}
    .code-number {{
      font-size: 14px;
      font-weight: bold;
      text-align: center;
    }}
    .code-container {{
      /* Dimensions : 3cm x 0.8cm */
      width: 3cm;
      height: 0.8cm;
      background: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      box-sizing: border-box;
      padding: 0;
      margin: 0;
    }}
    .code-img {{
      /* Remplit la zone blanche */
      width: 100%;
      height: 100%;
      object-fit: fill;
      display: block;
      border-radius: 4px;
    }}
  </style>
</head>
<body>
  <div id="labels"></div>
  <script>
    const label = document.createElement("div");
    label.className = "label";
    label.innerHTML = `
      <div class="logo"><img src="data:image/jpeg;base64,{logo_base64}" alt="Renault"></div>
      <div class="code-container">
        <img class="code-img" src="data:image/png;base64,{code_base64}" alt="{code_title}">
      </div>
      <div class="code-number">{code_value} {part_number}</div>
    `;
    document.getElementById("labels").appendChild(label);
  </script>
</body>
</html>"""
    
    filename = f"etiquette_sans_texte_{code_type}_{code_value.replace('-', '_')}_{part_number.replace('/', '_')}.html"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Étiquette créée: {filename}")
    return filename

def main():
    """Test code-barres sans texte intégré"""
    print("🎯 Test Code-barres SANS TEXTE (write_text=False)")
    print("=" * 55)
    
    # Vérifier qu'un logo existe
    logo_found = any(os.path.exists(path) for path in ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"])
    if not logo_found:
        print("❌ Logo Renault manquant!")
        return
    
    # Test code-barres
    print("\n📊 Test Code-barres (seulement barres)...")
    barcode_file = create_etiquette_sans_texte("64004380-0", "1/2", "barcode")
    
    # Test QR code
    print("\n📱 Test QR code...")
    qr_file = create_etiquette_sans_texte("64004380-0", "1/2", "qr")
    
    if barcode_file and qr_file:
        print(f"\n🎉 Tests terminés:")
        print(f"📁 Code-barres: {barcode_file}")
        print(f"📁 QR code: {qr_file}")
        print("\n💡 Vérifications:")
        print("✅ Code-barres: write_text=False (seulement barres)")
        print("✅ UN SEUL numéro affiché en bas de l'étiquette")
        print("✅ Mêmes dimensions pour les deux types")
        
        # Ouvrir le code-barres
        try:
            os.system(f"start {barcode_file}")
        except:
            print("   (Ouvrez manuellement le fichier)")

if __name__ == "__main__":
    main()
