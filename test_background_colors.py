#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script pour vérifier les couleurs de fond des tickets Renault
"""

import sys
import os

# Ajouter le répertoire parent au path pour importer app_native
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_background_colors():
    """Test des différentes couleurs de fond"""
    try:
        from app_native import SomacaGenerator
        
        print("=== Test des couleurs de fond des tickets Renault ===")
        
        # Créer une instance du générateur
        generator = SomacaGenerator()
        
        # Test avec fond noir (par défaut)
        print("\n1. Test avec fond NOIR (par défaut):")
        result_black = generator.generate_renault_etiquettes_with_choice(
            include_barcodes=True, 
            include_qr=True, 
            background_color="black"
        )
        print(f"Résultat fond noir: {result_black.get('success', False)}")
        
        # Test avec fond blanc
        print("\n2. Test avec fond BLANC:")
        result_white = generator.generate_renault_etiquettes_with_choice(
            include_barcodes=True, 
            include_qr=True, 
            background_color="white"
        )
        print(f"Résultat fond blanc: {result_white.get('success', False)}")
        
        print("\n=== Tests terminés ===")
        
        if result_black.get('success') and result_white.get('success'):
            print("✅ Tous les tests sont passés avec succès!")
        else:
            print("❌ Certains tests ont échoué")
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_background_colors()
