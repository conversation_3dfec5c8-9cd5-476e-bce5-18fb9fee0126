#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test QR code simple - SANS logo et SANS numéro pour diagnostiquer le problème
"""

import os
import base64
import qrcode
from PIL import Image
import io

def get_logo_base64():
    """Convertit le logo Renault en base64"""
    try:
        logo_paths = ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"]
        for logo_path in logo_paths:
            if os.path.exists(logo_path):
                with open(logo_path, "rb") as f:
                    logo_data = f.read()
                print(f"✅ Logo trouvé: {logo_path}")
                return base64.b64encode(logo_data).decode('utf-8')
        return ""
    except Exception as e:
        print(f"⚠️ Erreur: {e}")
        return ""

def generate_qr_simple(code_value):
    """Génère QR code 2cm x 2cm simple"""
    try:
        print(f"📱 Génération QR code simple pour: {code_value}")
        
        # <PERSON><PERSON>érer QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=1,
        )
        qr.add_data(code_value)
        qr.make(fit=True)
        
        # Créer l'image QR
        qr_img = qr.make_image(fill_color="black", back_color="white")
        
        print(f"📱 QR généré: {qr_img.size}")
        
        # Convertir en base64
        img_buffer = io.BytesIO()
        qr_img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        return base64.b64encode(img_buffer.getvalue()).decode('utf-8')
        
    except Exception as e:
        print(f"❌ Erreur QR code: {e}")
        return ""

def create_test_avec_logo(code_value="64004380-0", part_number="1/2"):
    """Test avec logo petit + numéro en bas"""

    qr_base64 = generate_qr_simple(code_value)
    logo_base64 = get_logo_base64()
    
    html_content = f"""<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Test QR Simple</title>
  <style>
    body {{
      margin: 0;
      padding: 20px;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
    }}
    .label {{
      /* Cadre : 7,0cm x 3,9cm */
      width: 7.0cm;
      height: 3.9cm;
      background-color: #000;
      border-radius: 8px;
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      font-family: Arial, sans-serif;
      margin: 0 auto;
      box-sizing: border-box;
      overflow: hidden;
      padding: 6px;
    }}
    .logo img {{
      height: 35px;  /* Logo plus petit pour laisser de la place */
    }}
    .code-number {{
      font-size: 10px;  /* Texte plus petit */
      font-weight: bold;
      text-align: center;
      margin-top: 3px;
    }}
    .qr-container {{
      /* Zone blanche finale : 2,2cm x 3,0cm */
      width: 2.2cm;
      height: 3.0cm;
      background: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      box-sizing: border-box;
      padding: 0;
      margin: 0;
    }}
    .qr-img {{
      /* QR code : 2cm x 2cm */
      width: 2cm;
      height: 2cm;
      object-fit: contain;
      display: block;
      border-radius: 2px;
    }}
  </style>
</head>
<body>
  <div id="labels"></div>
  <script>
    const label = document.createElement("div");
    label.className = "label";
    label.innerHTML = `
      <div class="logo"><img src="data:image/jpeg;base64,{logo_base64}" alt="Renault"></div>
      <div class="qr-container">
        <img class="qr-img" src="data:image/png;base64,{qr_base64}" alt="QR Code">
      </div>
      <div class="code-number">{code_value} {part_number}</div>
    `;
    document.getElementById("labels").appendChild(label);
  </script>
</body>
</html>"""
    
    filename = f"test_qr_logo_petit_{code_value.replace('-', '_')}.html"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Test avec logo créé: {filename}")
    return filename

def main():
    """Test QR avec logo petit + numéro en bas"""
    print("🧪 TEST QR AVEC LOGO PETIT + NUMÉRO")
    print("=" * 50)
    print("🔍 Diagnostic: zone blanche carrée 2,5cm x 2,5cm")
    print("📱 QR code: 2cm x 2cm centré")
    print("🏢 Logo Renault plus petit (35px)")
    print("🔢 Numéro en bas")
    print("🔴 Bordure rouge pour voir la zone blanche")
    print("=" * 50)

    # Créer le test
    filename = create_test_avec_logo("64004380-0", "1/2")
    
    if filename:
        print(f"\n📁 Fichier créé: {filename}")
        print("💡 Vérifiez si la zone blanche est bien carrée")
        print("🔴 La bordure rouge montre les limites exactes")
        
        # Ouvrir le fichier
        try:
            os.system(f"start {filename}")
        except:
            print("   (Ouvrez manuellement le fichier)")

if __name__ == "__main__":
    main()
