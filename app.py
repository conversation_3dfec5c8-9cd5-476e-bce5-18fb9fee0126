import eel
import os
import pandas as pd
import qrcode
from barcode import Code128
from barcode.writer import ImageWriter
import io
from openpyxl import Workbook
from openpyxl.drawing.image import Image as OpenpyxlImage
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
import barcode
import concurrent.futures
import threading
from functools import lru_cache
import base64
from PIL import Image

# Initialiser Eel
eel.init('web')

class SomacaBarcodeGenerator:
    def __init__(self):
        self.input_file = ""
        self.output_folder = ""
        self.progress = 0
        self.is_cancelled = False
        self.current_thread = None

    def cancel_operation(self):
        """Annuler l'opération en cours"""
        try:
            print("🛑 Demande d'annulation reçue")
            self.is_cancelled = True

            # Si un thread est en cours, essayer de l'arrêter proprement
            if self.current_thread and self.current_thread.is_alive():
                print("🛑 Arrêt du thread en cours...")
                # Le thread vérifiera self.is_cancelled et s'arrêtera

            return {"success": True, "message": "Opération annulée"}

        except Exception as e:
            print(f"❌ Erreur lors de l'annulation: {e}")
            return {"success": False, "message": f"Erreur lors de l'annulation: {str(e)}"}

    def set_input_file(self, file_path):
        """Définir le fichier d'entrée"""
        print(f"🔍 Tentative de définition du fichier: {file_path}")

        # Si c'est juste un nom de fichier, essayer de le trouver dans plusieurs endroits
        if not os.path.isabs(file_path) and not os.path.exists(file_path):
            search_paths = [
                os.getcwd(),  # Répertoire courant
                os.path.dirname(os.getcwd()),  # Répertoire parent
                os.path.join(os.getcwd(), "somaca_web_app"),  # Sous-dossier somaca_web_app
                os.path.join(os.path.dirname(os.getcwd()), "somaca_web_app"),  # Parent/somaca_web_app
            ]

            for search_dir in search_paths:
                full_path = os.path.join(search_dir, file_path)
                print(f"🔍 Recherche dans: {full_path}")
                if os.path.exists(full_path):
                    file_path = full_path
                    print(f"✅ Fichier trouvé: {file_path}")
                    break
            else:
                print(f"❌ Fichier non trouvé dans tous les répertoires de recherche")

        self.input_file = file_path
        print(f"📁 Fichier défini: {self.input_file}")
        return {"success": True, "message": f"Fichier sélectionné: {os.path.basename(file_path)}"}
    
    def set_output_folder(self, folder_path):
        """Définir le dossier de sortie"""
        self.output_folder = folder_path
        return {"success": True, "message": f"Dossier sélectionné: {folder_path}"}
    
    def get_progress(self):
        """Obtenir le progrès actuel"""
        return self.progress

    def update_progress_callback(self, percentage):
        """Callback pour mettre à jour la progression"""
        self.progress = percentage
        try:
            eel.update_progress(percentage)
        except:
            pass
    
    def read_excel_data_like_old_app(self, file_path):
        """Lit les données Excel de manière ADAPTATIVE : première ligne = en-têtes automatiquement"""
        try:
            # Lire TOUTES les lignes sans supposer d'en-têtes
            df_no_header = pd.read_excel(file_path, header=None)

            print(f"📊 Nombre total de lignes dans le fichier: {len(df_no_header)}")
            print(f"📊 Nombre de colonnes: {len(df_no_header.columns)}")

            if len(df_no_header) < 2:
                print("❌ Le fichier doit avoir au moins 2 lignes")
                return [], []

            # La PREMIÈRE ligne devient automatiquement les en-têtes
            first_row = df_no_header.iloc[0]
            headers = []
            for value in first_row:
                if pd.isna(value):
                    headers.append("Colonne_Vide")
                else:
                    headers.append(str(value).strip())

            print(f"📋 En-têtes détectés automatiquement (première ligne): {headers}")

            # Les lignes suivantes sont les données
            data = []
            for index in range(1, len(df_no_header)):
                row = df_no_header.iloc[index]
                row_data = []
                for value in row:
                    if pd.isna(value):
                        row_data.append("")
                    else:
                        row_data.append(str(value).strip())

                # Prendre toutes les lignes qui ont au moins une donnée
                if any(cell for cell in row_data if cell):
                    data.append(tuple(row_data))
                    print(f"📝 Ligne de données {index}: {row_data}")

            print(f"✅ {len(data)} lignes de données à traiter")
            print(f"🎯 En-têtes adaptatifs qui seront utilisés: {headers}")
            return data, headers
        except Exception as e:
            print(f"❌ Erreur lors de la lecture des données: {e}")
            import traceback
            traceback.print_exc()
            return [], []
    
    def generate_barcode_image(self, data):
        """Génère une image de code-barre OPTIMISÉE avec dimensions exactes : 3,5cm x 1,5cm"""
        try:
            # Optimisation : Réutiliser la classe code128 au lieu de la récupérer à chaque fois
            if not hasattr(self, '_code128_class'):
                self._code128_class = barcode.get_barcode_class('code128')

            # Optimisation : Configuration plus rapide
            barcode_instance = self._code128_class(data, writer=ImageWriter())

            buffer = io.BytesIO()
            barcode_instance.write(buffer)
            buffer.seek(0)

            img = OpenpyxlImage(buffer)

            # Dimensions optimisées (pré-calculées)
            img.width = 99   # 3,5 cm
            img.height = 43  # 1,5 cm

            return img
        except Exception as e:
            print(f"❌ Erreur génération code-barre pour {data}: {e}")
            return None
    
    def generate_qr_code_image(self, data):
        """Génère une image de QR code OPTIMISÉE avec dimensions exactes : 2cm x 2cm"""
        try:
            # Optimisation : Réutiliser la configuration QR
            if not hasattr(self, '_qr_config'):
                self._qr_config = {
                    'version': 1,
                    'error_correction': qrcode.constants.ERROR_CORRECT_L,
                    'box_size': 10,
                    'border': 4,
                }

            qr = qrcode.QRCode(**self._qr_config)
            qr.add_data(data)
            qr.make(fit=True)

            # Optimisation : Réutiliser les couleurs
            qr_img = qr.make_image(fill_color="black", back_color="white")

            buffer = io.BytesIO()
            qr_img.save(buffer, format='PNG')
            buffer.seek(0)

            img = OpenpyxlImage(buffer)

            # Dimensions optimisées (pré-calculées)
            img.width = 57   # 2 cm
            img.height = 57  # 2 cm

            return img
        except Exception as e:
            print(f"❌ Erreur génération QR code pour {data}: {e}")
            return None

    def generate_high_res_barcode(self, data, save_path):
        """Génère un code-barres HAUTE RÉSOLUTION pour impression : 3,5cm x 1,5cm"""
        try:
            code128 = barcode.get_barcode_class('code128')
            barcode_instance = code128(data, writer=ImageWriter())

            # Sauvegarder directement en haute résolution
            barcode_instance.save(save_path)

            # Redimensionner à la taille exacte avec PIL pour haute résolution
            from PIL import Image
            img = Image.open(save_path)

            # Haute résolution : 300 DPI
            # 3,5 cm à 300 DPI = 413 pixels
            # 1,5 cm à 300 DPI = 177 pixels
            img_resized = img.resize((413, 177), Image.Resampling.LANCZOS)
            img_resized.save(save_path, 'PNG', dpi=(300, 300))

            print(f"📏 Code-barres haute résolution sauvé: {save_path}")
            return True
        except Exception as e:
            print(f"❌ Erreur génération code-barres HR pour {data}: {e}")
            return False

    def generate_high_res_qr_code(self, data, save_path):
        """Génère un QR code HAUTE RÉSOLUTION pour impression : 2cm x 2cm"""
        try:
            # Configuration haute résolution
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=20,  # Plus grand pour haute résolution
                border=8,     # Bordure proportionnelle
            )
            qr.add_data(data)
            qr.make(fit=True)

            qr_img = qr.make_image(fill_color="black", back_color="white")

            # Redimensionner à la taille exacte avec haute résolution
            from PIL import Image
            # 2 cm à 300 DPI = 236 pixels
            qr_img_resized = qr_img.resize((236, 236), Image.Resampling.LANCZOS)
            qr_img_resized.save(save_path, 'PNG', dpi=(300, 300))

            print(f"📱 QR code haute résolution sauvé: {save_path}")
            return True
        except Exception as e:
            print(f"❌ Erreur génération QR code HR pour {data}: {e}")
            return False

    def generate_high_res_qr_code_with_text(self, qr_data, descriptive_text, save_path):
        """Génère un QR code HAUTE RÉSOLUTION avec texte descriptif au-dessus : 2,5cm x 3cm"""
        try:
            from PIL import Image, ImageDraw, ImageFont

            # Configuration haute résolution pour QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=15,  # Taille adaptée pour laisser place au texte
                border=4,
            )
            qr.add_data(qr_data)
            qr.make(fit=True)

            qr_img = qr.make_image(fill_color="black", back_color="white")

            # Dimensions finales : 2,5cm x 3cm à 300 DPI
            final_width = int(2.5 * 300 / 2.54)  # 295 pixels
            final_height = int(3.0 * 300 / 2.54)  # 354 pixels

            # Créer l'image finale avec fond blanc
            final_img = Image.new('RGB', (final_width, final_height), 'white')
            draw = ImageDraw.Draw(final_img)

            # Charger la police pour le texte
            try:
                font = ImageFont.truetype("arial.ttf", 14)
            except:
                font = ImageFont.load_default()

            # Calculer la hauteur du texte
            text_lines = descriptive_text.split('\n')
            line_height = 18
            text_height = len(text_lines) * line_height + 10  # +10 pour l'espacement

            # Redimensionner le QR code pour qu'il tienne avec le texte
            qr_size = min(final_width - 20, final_height - text_height - 20)
            qr_img_resized = qr_img.resize((qr_size, qr_size), Image.Resampling.LANCZOS)

            # Positionner le texte en haut, centré
            text_y = 10
            for line in text_lines:
                # Calculer la largeur du texte pour le centrer
                bbox = draw.textbbox((0, 0), line, font=font)
                text_width = bbox[2] - bbox[0]
                text_x = (final_width - text_width) // 2
                draw.text((text_x, text_y), line, fill='black', font=font)
                text_y += line_height

            # Positionner le QR code en bas, centré
            qr_x = (final_width - qr_size) // 2
            qr_y = text_height + 10
            final_img.paste(qr_img_resized, (qr_x, qr_y))

            # Sauvegarder l'image finale
            final_img.save(save_path, 'PNG', dpi=(300, 300))

            print(f"📱 QR code avec texte haute résolution sauvé: {save_path}")
            return True
        except Exception as e:
            print(f"❌ Erreur génération QR code avec texte pour {qr_data}: {e}")
            return False

    def generate_high_res_barcode_with_text(self, barcode_data, descriptive_text, save_path):
        """Génère un code-barres HAUTE RÉSOLUTION avec texte descriptif au-dessus : 3,5cm x 2,5cm"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import barcode
            from barcode.writer import ImageWriter

            # Générer le code-barres de base
            code128 = barcode.get_barcode_class('code128')
            barcode_instance = code128(barcode_data, writer=ImageWriter())

            # Créer l'image du code-barres en mémoire
            barcode_buffer = io.BytesIO()
            barcode_instance.write(barcode_buffer)
            barcode_buffer.seek(0)
            barcode_img = Image.open(barcode_buffer)

            # Dimensions finales : 3,5cm x 2,5cm à 300 DPI
            final_width = int(3.5 * 300 / 2.54)  # 413 pixels
            final_height = int(2.5 * 300 / 2.54)  # 295 pixels

            # Créer l'image finale avec fond blanc
            final_img = Image.new('RGB', (final_width, final_height), 'white')
            draw = ImageDraw.Draw(final_img)

            # Charger la police pour le texte
            try:
                font = ImageFont.truetype("arial.ttf", 12)
            except:
                font = ImageFont.load_default()

            # Calculer la hauteur du texte
            text_lines = descriptive_text.split('\n')
            line_height = 16
            text_height = len(text_lines) * line_height + 10  # +10 pour l'espacement

            # Redimensionner le code-barres pour qu'il tienne avec le texte
            barcode_height = final_height - text_height - 20
            barcode_width = final_width - 20
            barcode_img_resized = barcode_img.resize((barcode_width, barcode_height), Image.Resampling.LANCZOS)

            # Positionner le texte en haut, centré
            text_y = 10
            for line in text_lines:
                # Calculer la largeur du texte pour le centrer
                bbox = draw.textbbox((0, 0), line, font=font)
                text_width = bbox[2] - bbox[0]
                text_x = (final_width - text_width) // 2
                draw.text((text_x, text_y), line, fill='black', font=font)
                text_y += line_height

            # Positionner le code-barres en bas, centré
            barcode_x = (final_width - barcode_width) // 2
            barcode_y = text_height + 10
            final_img.paste(barcode_img_resized, (barcode_x, barcode_y))

            # Sauvegarder l'image finale
            final_img.save(save_path, 'PNG', dpi=(300, 300))

            print(f"📏 Code-barres avec texte haute résolution sauvé: {save_path}")
            return True
        except Exception as e:
            print(f"❌ Erreur génération code-barres avec texte pour {barcode_data}: {e}")
            return False

    def generate_framed_barcode(self, barcode_data, descriptive_text, save_path):
        """Génère un code-barres dans un cadre 6cm x 3cm avec logo Renault"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import barcode
            from barcode.writer import ImageWriter

            # Dimensions du cadre : 6cm x 3cm à 300 DPI
            frame_width = int(6.0 * 300 / 2.54)  # 709 pixels
            frame_height = int(3.0 * 300 / 2.54)  # 354 pixels

            # Créer l'image du cadre avec fond noir (comme dans votre exemple)
            frame_img = Image.new('RGB', (frame_width, frame_height), '#1a1a1a')
            draw = ImageDraw.Draw(frame_img)

            # Charger le logo Renault
            logo_path = "renault-logo.png"
            if os.path.exists(logo_path):
                try:
                    logo = Image.open(logo_path)
                    # Redimensionner le logo pour qu'il tienne dans le coin supérieur gauche
                    logo_size = 60  # Taille du logo en pixels
                    logo = logo.resize((logo_size, logo_size), Image.Resampling.LANCZOS)
                    # Positionner le logo en haut à gauche avec une petite marge
                    frame_img.paste(logo, (10, 10), logo if logo.mode == 'RGBA' else None)
                except Exception as logo_error:
                    print(f"⚠️ Erreur chargement logo: {logo_error}")

            # Générer le code-barres
            code128 = barcode.get_barcode_class('code128')
            barcode_instance = code128(barcode_data, writer=ImageWriter())

            barcode_buffer = io.BytesIO()
            barcode_instance.write(barcode_buffer)
            barcode_buffer.seek(0)
            barcode_img = Image.open(barcode_buffer)

            # Zone disponible pour le contenu (en laissant de la place pour les bordures)
            content_margin = 20
            content_width = frame_width - 2 * content_margin
            content_height = frame_height - 2 * content_margin

            # Charger la police pour le texte
            try:
                font = ImageFont.truetype("arial.ttf", 10)
            except:
                font = ImageFont.load_default()

            # Calculer l'espace pour le texte descriptif
            text_lines = descriptive_text.split('\n')
            line_height = 14
            text_height = len(text_lines) * line_height + 10

            # Redimensionner le code-barres pour qu'il tienne dans l'espace disponible
            barcode_height = content_height - text_height - 30
            barcode_width = min(content_width - 20, int(barcode_height * 3))  # Ratio approximatif des codes-barres
            barcode_img_resized = barcode_img.resize((barcode_width, barcode_height), Image.Resampling.LANCZOS)

            # Positionner le texte descriptif en haut
            text_y = content_margin + 10
            for line in text_lines:
                bbox = draw.textbbox((0, 0), line, font=font)
                text_width = bbox[2] - bbox[0]
                text_x = content_margin + (content_width - text_width) // 2
                draw.text((text_x, text_y), line, fill='white', font=font)
                text_y += line_height

            # Positionner le code-barres au centre
            barcode_x = content_margin + (content_width - barcode_width) // 2
            barcode_y = text_y + 10
            frame_img.paste(barcode_img_resized, (barcode_x, barcode_y))

            # Ajouter le code en bas de l'image
            code_text = barcode_data
            bbox = draw.textbbox((0, 0), code_text, font=font)
            code_width = bbox[2] - bbox[0]
            code_x = content_margin + (content_width - code_width) // 2
            code_y = frame_height - content_margin - 20
            draw.text((code_x, code_y), code_text, fill='white', font=font)

            # Sauvegarder l'image finale
            frame_img.save(save_path, 'PNG', dpi=(300, 300))
            print(f"🖼️ Code-barres encadré sauvé: {save_path}")
            return True

        except Exception as e:
            print(f"❌ Erreur génération code-barres encadré pour {barcode_data}: {e}")
            return False

    def generate_framed_qr_code(self, qr_data, descriptive_text, save_path):
        """Génère un QR code dans un cadre 6cm x 3cm avec logo Renault"""
        try:
            from PIL import Image, ImageDraw, ImageFont

            # Dimensions du cadre : 6cm x 3cm à 300 DPI
            frame_width = int(6.0 * 300 / 2.54)  # 709 pixels
            frame_height = int(3.0 * 300 / 2.54)  # 354 pixels

            # Créer l'image du cadre avec fond noir (comme dans votre exemple)
            frame_img = Image.new('RGB', (frame_width, frame_height), '#1a1a1a')
            draw = ImageDraw.Draw(frame_img)

            # Charger le logo Renault
            logo_path = "renault-logo.png"
            if os.path.exists(logo_path):
                try:
                    logo = Image.open(logo_path)
                    # Redimensionner le logo pour qu'il tienne dans le coin supérieur gauche
                    logo_size = 60  # Taille du logo en pixels
                    logo = logo.resize((logo_size, logo_size), Image.Resampling.LANCZOS)
                    # Positionner le logo en haut à gauche avec une petite marge
                    frame_img.paste(logo, (10, 10), logo if logo.mode == 'RGBA' else None)
                except Exception as logo_error:
                    print(f"⚠️ Erreur chargement logo: {logo_error}")

            # Générer le QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=8,
                border=2,
            )
            qr.add_data(qr_data)
            qr.make(fit=True)
            qr_img = qr.make_image(fill_color="black", back_color="white")

            # Zone disponible pour le contenu
            content_margin = 20
            content_width = frame_width - 2 * content_margin
            content_height = frame_height - 2 * content_margin

            # Charger la police pour le texte
            try:
                font = ImageFont.truetype("arial.ttf", 10)
            except:
                font = ImageFont.load_default()

            # Calculer l'espace pour le texte descriptif
            text_lines = descriptive_text.split('\n')
            line_height = 14
            text_height = len(text_lines) * line_height + 10

            # Redimensionner le QR code pour qu'il tienne dans l'espace disponible
            qr_size = min(content_width - 20, content_height - text_height - 50)
            qr_img_resized = qr_img.resize((qr_size, qr_size), Image.Resampling.LANCZOS)

            # Positionner le texte descriptif en haut
            text_y = content_margin + 10
            for line in text_lines:
                bbox = draw.textbbox((0, 0), line, font=font)
                text_width = bbox[2] - bbox[0]
                text_x = content_margin + (content_width - text_width) // 2
                draw.text((text_x, text_y), line, fill='white', font=font)
                text_y += line_height

            # Positionner le QR code au centre
            qr_x = content_margin + (content_width - qr_size) // 2
            qr_y = text_y + 10
            frame_img.paste(qr_img_resized, (qr_x, qr_y))

            # Ajouter un texte en bas (première partie du QR data)
            display_text = qr_data.split('|')[0] if '|' in qr_data else qr_data[:20] + "..."
            bbox = draw.textbbox((0, 0), display_text, font=font)
            text_width = bbox[2] - bbox[0]
            text_x = content_margin + (content_width - text_width) // 2
            text_y = frame_height - content_margin - 20
            draw.text((text_x, text_y), display_text, fill='white', font=font)

            # Sauvegarder l'image finale
            frame_img.save(save_path, 'PNG', dpi=(300, 300))
            print(f"🖼️ QR code encadré sauvé: {save_path}")
            return True

        except Exception as e:
            print(f"❌ Erreur génération QR code encadré pour {qr_data}: {e}")
            return False

    def generate_html_based_frame(self, code_value, code_type="barcode", part_number="1/2"):
        """Génère une image d'étiquette basée sur HTML/CSS pour un rendu parfait"""
        try:
            import tempfile
            import subprocess
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.common.by import By
            import time

            # Template HTML pour l'étiquette
            html_template = f"""
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Étiquette Renault {code_type.upper()}</title>
  <style>
    body {{
      margin: 0;
      padding: 20px;
      background: #f0f0f0;
    }}
    .label {{
      width: 6cm;
      height: 3cm;
      background: #2a2a2a;
      border-radius: 8px;
      color: #fff;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      padding: 10px;
      font-family: Arial, sans-serif;
      position: relative;
      box-sizing: border-box;
    }}

    /* Rivets dorés dans les coins */
    .label::before,
    .label::after {{
      content: '';
      position: absolute;
      width: 8px;
      height: 8px;
      background: #FFD700;
      border-radius: 50%;
    }}
    .label::before {{
      top: 8px;
      left: 8px;
    }}
    .label::after {{
      top: 8px;
      right: 8px;
    }}
    .rivet-bottom-left,
    .rivet-bottom-right {{
      position: absolute;
      width: 8px;
      height: 8px;
      background: #FFD700;
      border-radius: 50%;
      bottom: 8px;
    }}
    .rivet-bottom-left {{ left: 8px; }}
    .rivet-bottom-right {{ right: 8px; }}

    .logo img {{
      height: 40px;
      margin-bottom: 5px;
    }}

    .barcode {{
      font-family: 'Libre Barcode 128', monospace;
      font-size: 28px;
      background: #fff;
      color: #000;
      padding: 4px 8px;
      border-radius: 2px;
      letter-spacing: 1px;
    }}

    .qrcode {{
      background: #fff;
      padding: 4px;
      border-radius: 2px;
    }}

    .code-text {{
      font-size: 16px;
      font-weight: bold;
      text-align: center;
      margin-top: 5px;
    }}
  </style>
  <link href="https://fonts.googleapis.com/css2?family=Libre+Barcode+128&display=swap" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/qrcode/build/qrcode.min.js"></script>
</head>
<body>
  <div class="label" id="label">
    <div class="rivet-bottom-left"></div>
    <div class="rivet-bottom-right"></div>

    <div class="logo">
      <img src="data:image/png;base64,{self.get_logo_base64()}" alt="Renault Logo">
    </div>

    {"<div class='barcode'>*" + code_value + "*</div>" if code_type == "barcode" else "<div class='qrcode' id='qrcode'></div>"}

    <div class="code-text">{code_value} {part_number}</div>
  </div>

  <script>
    {"" if code_type == "barcode" else f"""
    const canvas = document.createElement("canvas");
    document.getElementById("qrcode").appendChild(canvas);
    QRCode.toCanvas(canvas, "{code_value}", {{ width: 80, margin: 1 }}, function(error) {{
      if (error) console.error(error);
    }});
    """}
  </script>
</body>
</html>
            """

            # Créer un fichier HTML temporaire
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_template)
                html_file = f.name

            # Configurer Chrome en mode headless
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--window-size=800,600')

            # Créer le driver
            driver = webdriver.Chrome(options=chrome_options)

            try:
                # Charger la page HTML
                driver.get(f'file://{html_file}')
                time.sleep(2)  # Attendre le rendu

                # Prendre une capture d'écran de l'élément label
                label_element = driver.find_element(By.ID, "label")
                screenshot_path = f"etiquette_{code_type}_{code_value.replace('-', '_')}_{part_number.replace('/', '_')}.png"
                label_element.screenshot(screenshot_path)

                print(f"✅ Étiquette HTML générée: {screenshot_path}")
                return screenshot_path

            finally:
                driver.quit()
                os.unlink(html_file)  # Supprimer le fichier temporaire

        except ImportError:
            print("⚠️ Selenium non installé. Installation requise: pip install selenium")
            return None
        except Exception as e:
            print(f"❌ Erreur génération étiquette HTML: {e}")
            return None

    def get_logo_base64(self):
        """Convertit le logo Renault en base64 pour l'intégrer dans le HTML"""
        try:
            logo_paths = ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"]
            for logo_path in logo_paths:
                if os.path.exists(logo_path):
                    with open(logo_path, "rb") as f:
                        logo_data = f.read()
                    return base64.b64encode(logo_data).decode('utf-8')
            return ""
        except Exception as e:
            print(f"⚠️ Erreur conversion logo: {e}")
            return ""

    def generate_renault_barcode(self, barcode_data):
        """Génère code-barres Renault 4,5cm x 3cm avec 2 premières colonnes seulement"""
        try:
            # Dimensions finales : 4,5cm x 3cm
            target_width_cm = 4.5
            target_height_cm = 3.0
            dpi = 300
            target_width_px = int(target_width_cm * dpi / 2.54)  # 531 pixels
            target_height_px = int(target_height_cm * dpi / 2.54)  # 354 pixels

            # Générer code-barres
            code128 = barcode.get_barcode_class('code128')
            writer = ImageWriter()
            writer.set_options({
                'module_width': 0.6,    # Barres plus larges
                'module_height': 35,    # Barres plus hautes
                'quiet_zone': 1,
                'dpi': dpi,
                'background': 'white',
                'foreground': 'black',
            })
            barcode_instance = code128(barcode_data, writer=writer)

            # Générer l'image
            buffer = io.BytesIO()
            barcode_instance.write(buffer)
            buffer.seek(0)

            barcode_img = Image.open(buffer)
            width, height = barcode_img.size

            # COUPER la partie texte (garder seulement les barres)
            cut_height = int(height * 0.7)
            barcode_only_bars = barcode_img.crop((0, 0, width, cut_height))

            # Redimensionner exactement à 4,5cm x 3cm
            barcode_final = barcode_only_bars.resize((target_width_px, target_height_px), Image.Resampling.LANCZOS)

            return base64.b64encode(self._pil_to_bytes(barcode_final)).decode('utf-8')

        except Exception as e:
            print(f"❌ Erreur code-barres Renault: {e}")
            return ""

    def generate_renault_qr(self, qr_data):
        """Génère QR code Renault 2cm x 2cm avec TOUTES les données"""
        try:
            # Générer QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=1,
            )
            qr.add_data(qr_data)
            qr.make(fit=True)

            # Créer l'image QR
            qr_img = qr.make_image(fill_color="black", back_color="white")

            return base64.b64encode(self._pil_to_bytes(qr_img)).decode('utf-8')

        except Exception as e:
            print(f"❌ Erreur QR code Renault: {e}")
            return ""

    def _pil_to_bytes(self, pil_image):
        """Convertit une image PIL en bytes"""
        img_buffer = io.BytesIO()
        pil_image.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        return img_buffer.getvalue()

    def create_renault_etiquette(self, row_data, code_type="barcode"):
        """Créer étiquette Renault avec données de ligne Excel

        Args:
            row_data: Liste des données de la ligne Excel
            code_type: "barcode" ou "qr"
        """

        logo_base64 = self.get_logo_base64()

        # Vérifier qu'on a au moins 2 colonnes
        if len(row_data) < 2:
            print("❌ Erreur: Il faut au moins 2 colonnes de données")
            return None

        # Les 2 premières colonnes pour l'affichage en bas avec séparation -
        display_text = f"{row_data[0]} - {row_data[1]}"

        if code_type == "barcode":
            # Code-barres: seulement les 2 premières colonnes (nettoyer les caractères spéciaux)
            clean_data1 = str(row_data[0]).replace('è', 'e').replace('é', 'e').replace('à', 'a').replace('ç', 'c')
            clean_data2 = str(row_data[1]).replace('è', 'e').replace('é', 'e').replace('à', 'a').replace('ç', 'c')
            barcode_data = f"{clean_data1}|{clean_data2}"
            code_base64 = self.generate_renault_barcode(barcode_data)
            code_title = "Code-barres"
            container_width = "4.5cm"
            container_height = "3cm"
            code_width = "4.5cm"
            code_height = "3cm"
        else:
            # QR code: TOUTES les données de la ligne
            qr_data = "|".join(str(item) for item in row_data)
            code_base64 = self.generate_renault_qr(qr_data)
            code_title = "QR Code"
            container_width = "2.2cm"  # Zone blanche finale
            container_height = "3.0cm"  # Zone blanche finale
            code_width = "2cm"  # QR code centré
            code_height = "2cm"

        html_content = f"""<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Étiquette Renault - {code_title}</title>
  <style>
    body {{
      margin: 0;
      padding: 20px;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
    }}
    .label {{
      /* Cadre final : 7,0cm x 3,9cm */
      width: 7.0cm;
      height: 3.9cm;
      background-color: #000;
      border-radius: 8px;
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      font-family: Arial, sans-serif;
      margin: 0 auto;
      box-sizing: border-box;
      overflow: hidden;
      padding: 6px;
    }}
    .logo img {{
      height: 35px;  /* Logo petit pour laisser de la place */
    }}
    .code-number {{
      font-size: 10px;  /* Texte petit */
      font-weight: bold;
      text-align: center;
      margin-top: 3px;
    }}
    .code-container {{
      /* Zone blanche selon le type */
      width: {container_width};
      height: {container_height};
      background: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      box-sizing: border-box;
      padding: 0;
      margin: 0;
    }}
    .code-img {{
      /* Code - dimensions exactes */
      width: {code_width};
      height: {code_height};
      object-fit: contain;
      display: block;
      border-radius: 4px;
    }}
  </style>
</head>
<body>
  <div id="labels"></div>
  <script>
    const label = document.createElement("div");
    label.className = "label";
    label.innerHTML = `
      <div class="logo"><img src="data:image/jpeg;base64,{logo_base64}" alt="Renault"></div>
      <div class="code-container">
        <img class="code-img" src="data:image/png;base64,{code_base64}" alt="{code_title}">
      </div>
      <div class="code-number">{display_text}</div>
    `;
    document.getElementById("labels").appendChild(label);
  </script>
</body>
</html>"""

        return html_content
    
    def setup_enhanced_headers(self, worksheet, original_headers=None, generate_barcodes=True, generate_qr=True):
        """Configure des en-têtes avec les colonnes originales + nouvelles colonnes selon le choix utilisateur"""
        if original_headers:
            # Commencer avec les en-têtes originaux EXACTEMENT comme dans le fichier source
            headers = list(original_headers)

            # Ajouter les nouvelles colonnes SEULEMENT selon le choix de l'utilisateur
            if generate_barcodes:
                headers.append("Code-Barre")
            if generate_qr:
                headers.append("QR Code")
        else:
            # Fallback vers les en-têtes par défaut
            headers = ["Numéro", "Référence", "Description"]
            if generate_barcodes:
                headers.append("Code-Barre")
            if generate_qr:
                headers.append("QR Code")
        
        # Styles améliorés
        header_font = Font(bold=True, color="FFFFFF", size=12)
        header_fill = PatternFill(start_color="2E86AB", end_color="2E86AB", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        border = Border(
            left=Side(style='thin', color='FFFFFF'),
            right=Side(style='thin', color='FFFFFF'),
            top=Side(style='thin', color='FFFFFF'),
            bottom=Side(style='thin', color='FFFFFF')
        )
        
        # Ajouter les en-têtes
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = border
        
        # Ajuster les largeurs des colonnes dynamiquement
        num_cols = len(headers)

        for col_idx in range(num_cols):
            col_letter = chr(ord('A') + col_idx)
            header_name = headers[col_idx]

            if header_name == "Code-Barre":
                # Largeur adaptée aux codes-barres 3,5cm (environ 15 unités Excel)
                width = 15
            elif header_name == "QR Code":
                # Largeur adaptée aux QR codes 2cm (environ 10 unités Excel)
                width = 10
            else:
                # Largeur adaptative pour les colonnes de données originales
                if col_idx == 0:
                    width = 15  # Première colonne (souvent numéro)
                elif col_idx == 1:
                    width = 25  # Deuxième colonne (souvent référence)
                else:
                    width = 20  # Autres colonnes de données

            worksheet.column_dimensions[col_letter].width = width
        
        # Hauteur de la ligne d'en-tête
        worksheet.row_dimensions[1].height = 25
    
    def add_enhanced_data_row(self, worksheet, row_num, row_data, original_headers, generate_barcodes=True, generate_qr=True):
        """Ajoute une ligne de données OPTIMISÉE avec un nombre variable de colonnes"""
        num_original_cols = len(original_headers)
        # Optimisation : Réutiliser les styles (créés une seule fois)
        if not hasattr(self, '_data_font'):
            self._data_font = Font(size=10)
            self._data_alignment = Alignment(horizontal="center", vertical="center")

        data_font = self._data_font
        data_alignment = self._data_alignment

        # Couleur alternée pour les lignes
        if row_num % 2 == 0:
            fill_color = PatternFill(start_color="F8F9FA", end_color="F8F9FA", fill_type="solid")
        else:
            fill_color = PatternFill(start_color="FFFFFF", end_color="FFFFFF", fill_type="solid")

        # Ajouter toutes les données originales
        for col, data in enumerate(row_data, 1):
            cell = worksheet.cell(row=row_num, column=col, value=data)
            cell.font = data_font
            cell.alignment = data_alignment
            cell.fill = fill_color
        
        # Générer les codes basés sur les données de la ligne
        # Code-barres : SEULEMENT les 2 premières colonnes
        col1 = row_data[0] if len(row_data) > 0 else ""
        col2 = row_data[1] if len(row_data) > 1 else ""
        barcode_data = f"{col1}-{col2}"

        # QR Code : TOUTES les données avec les VRAIS noms des colonnes
        qr_data_parts = []
        for i, value in enumerate(row_data):
            if value and i < len(original_headers):  # Seulement si la valeur n'est pas vide et qu'on a l'en-tête
                header_name = original_headers[i]
                qr_data_parts.append(f"{header_name}:{value}")
        qr_data = "|".join(qr_data_parts)

        # Logs réduits pour accélérer (seulement en mode debug)
        # print(f"🏷️ Code-barres: {barcode_data}")
        # print(f"📱 QR Code: {qr_data}")

        # Calculer les positions des colonnes pour les codes selon les options
        barcode_col = None
        qr_col = None

        next_col = num_original_cols + 1
        if generate_barcodes:
            barcode_col = next_col
            next_col += 1
        if generate_qr:
            qr_col = next_col

        # Code-barre (seulement si demandé)
        if generate_barcodes and barcode_col:
            # Créer une cellule vide avec le style approprié
            barcode_cell = worksheet.cell(row=row_num, column=barcode_col, value="")
            barcode_cell.font = data_font
            barcode_cell.alignment = data_alignment
            barcode_cell.fill = fill_color

            # Ajouter SEULEMENT l'image du code-barres
            barcode_img = self.generate_barcode_image(barcode_data)
            if barcode_img:
                barcode_col_letter = chr(ord('A') + barcode_col - 1)
                barcode_img.anchor = f"{barcode_col_letter}{row_num}"
                worksheet.add_image(barcode_img)
                print(f"📊 Code-barres ajouté: {barcode_data}")

        # QR Code (seulement si demandé)
        if generate_qr and qr_col:
            # Créer une cellule vide avec le style approprié
            qr_cell = worksheet.cell(row=row_num, column=qr_col, value="")
            qr_cell.font = data_font
            qr_cell.alignment = data_alignment
            qr_cell.fill = fill_color

            # Ajouter SEULEMENT l'image du QR code
            qr_img = self.generate_qr_code_image(qr_data)
            if qr_img:
                qr_col_letter = chr(ord('A') + qr_col - 1)
                qr_img.anchor = f"{qr_col_letter}{row_num}"
                worksheet.add_image(qr_img)
                print(f"📱 QR code ajouté: {qr_data[:50]}...")

        # Ajuster la hauteur de la ligne selon les codes générés
        if generate_barcodes and generate_qr:
            # Hauteur pour QR codes 2cm (environ 43 points Excel)
            # 1 cm ≈ 21.3 points Excel, donc 2 cm ≈ 43 points
            worksheet.row_dimensions[row_num].height = 45
        elif generate_qr:
            # Hauteur pour QR codes seulement (2cm)
            worksheet.row_dimensions[row_num].height = 45
        elif generate_barcodes:
            # Hauteur pour codes-barres seulement (1,5cm)
            worksheet.row_dimensions[row_num].height = 35
        else:
            worksheet.row_dimensions[row_num].height = 25
    
    def cleanup_open_files(self):
        """Nettoyer tous les fichiers ouverts pour éviter les conflits"""
        try:
            import gc
            # Forcer la collecte des déchets pour libérer les fichiers
            gc.collect()
            print("🧹 Nettoyage des fichiers ouverts effectué")
        except Exception as e:
            print(f"Erreur nettoyage: {e}")

    def generate_codes(self, generate_barcodes=True, generate_qr=True):
        """Générer un fichier Excel EXACTEMENT comme l'ancienne application PyQt"""
        try:
            # Nettoyer les fichiers ouverts avant de commencer
            self.cleanup_open_files()

            if not self.input_file or not self.output_folder:
                return {"success": False, "message": "Veuillez sélectionner un fichier et un dossier de destination"}

            # Vérifier que le fichier existe
            if not os.path.exists(self.input_file):
                return {"success": False, "message": f"Fichier non trouvé: {self.input_file}"}

            # Vérifier que le dossier de sortie existe
            if not os.path.exists(self.output_folder):
                return {"success": False, "message": f"Dossier non trouvé: {self.output_folder}"}
            
            print(f"🚀 Traitement du fichier: {self.input_file}")
            
            # Lire les données en préservant les en-têtes originaux
            data, original_headers = self.read_excel_data_like_old_app(self.input_file)
            if not data:
                return {"success": False, "message": "Aucune donnée à traiter"}

            print(f"✅ {len(data)} lignes de données lues")
            print(f"📋 En-têtes originaux: {original_headers}")

            # Créer un nouveau workbook avec les en-têtes originaux
            workbook = Workbook()
            worksheet = workbook.active
            worksheet.title = "Codes-Barres et QR"

            # Configuration des en-têtes avec les colonnes originales + nouvelles selon le choix
            self.setup_enhanced_headers(worksheet, original_headers, generate_barcodes, generate_qr)

            # Traitement EN TEMPS RÉEL avec progression visible
            total_items = len(data)
            print(f"🚀 Traitement en temps réel de {total_items} lignes...")

            # Traitement ligne par ligne avec mise à jour immédiate
            for i, row_data in enumerate(data, 2):
                # Vérifier si l'opération a été annulée
                if self.is_cancelled:
                    print("🛑 Opération annulée par l'utilisateur")
                    return {"success": False, "message": "Opération annulée par l'utilisateur"}

                print(f"📊 Traitement ligne {i-1}/{total_items}")

                # Générer et ajouter la ligne immédiatement
                self.add_enhanced_data_row(worksheet, i, row_data, original_headers, generate_barcodes, generate_qr)

                # Mise à jour de progression IMMÉDIATE après chaque ligne
                progress = (i-1) / total_items
                self.progress = int(progress * 100)
                try:
                    eel.update_progress(self.progress)
                    # Petit délai pour permettre la mise à jour de l'interface
                    import time
                    time.sleep(0.01)  # 10ms pour permettre la mise à jour
                except:
                    pass
            
            # Vérifier si le fichier existe déjà
            output_filename = f"SOMACA_Codes_{os.path.splitext(os.path.basename(self.input_file))[0]}.xlsx"
            output_path = os.path.join(self.output_folder, output_filename)

            if os.path.exists(output_path):
                # Fichier existe déjà - demander à l'utilisateur
                print(f"⚠️ Fichier existe déjà: {output_path}")
                try:
                    eel.file_exists_dialog(output_filename)
                    # Attendre la réponse de l'utilisateur
                    import time
                    timeout = 30  # 30 secondes max
                    while not hasattr(self, '_user_choice') and timeout > 0:
                        time.sleep(0.1)
                        timeout -= 0.1

                    if hasattr(self, '_user_choice'):
                        choice = self._user_choice
                        delattr(self, '_user_choice')

                        if choice == 'cancel':
                            return {"success": False, "message": "Génération annulée par l'utilisateur"}
                        elif choice == 'rename':
                            # Générer un nouveau nom avec timestamp
                            import datetime
                            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                            base_name = os.path.splitext(output_filename)[0]
                            output_filename = f"{base_name}_{timestamp}.xlsx"
                            output_path = os.path.join(self.output_folder, output_filename)
                        # Si choice == 'overwrite', on garde le même chemin
                    else:
                        # Timeout - utiliser un nom avec timestamp par défaut
                        import datetime
                        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                        base_name = os.path.splitext(output_filename)[0]
                        output_filename = f"{base_name}_{timestamp}.xlsx"
                        output_path = os.path.join(self.output_folder, output_filename)
                except:
                    # En cas d'erreur, utiliser un nom avec timestamp
                    import datetime
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    base_name = os.path.splitext(output_filename)[0]
                    output_filename = f"{base_name}_{timestamp}.xlsx"
                    output_path = os.path.join(self.output_folder, output_filename)

            workbook.save(output_path)
            workbook.close()  # Fermer explicitement le workbook
            print(f"✅ Fichier sauvegardé: {output_path}")

            # Forcer la libération des ressources
            import gc
            gc.collect()

            # Sauvegarder le chemin pour l'extraction d'images ULTRA-RAPIDE
            self.last_generated_file = output_path

            self.progress = 100
            try:
                eel.update_progress(100)
            except:
                pass

            return {
                "success": True,
                "message": f"Fichier Excel généré avec succès ! {total_items} codes intégrés.\nFichier: {output_filename}",
                "generated": total_items,
                "output_file": output_path
            }
            
        except Exception as e:
            print(f"❌ Erreur lors du traitement: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "message": f"Erreur: {str(e)}"}

    def export_images_for_print(self, export_barcodes=True, export_qr=True):
        """Extrait DIRECTEMENT les images du fichier Excel généré selon les choix utilisateur - ULTRA RAPIDE !"""
        try:
            if not hasattr(self, 'last_generated_file') or not self.last_generated_file:
                return {"success": False, "message": "Aucun fichier Excel généré à extraire"}

            if not os.path.exists(self.last_generated_file):
                return {"success": False, "message": "Le fichier Excel généré n'existe plus"}

            print("🚀 Génération des images avec texte descriptif...")

            # Créer les dossiers d'export selon les choix utilisateur
            base_name = os.path.splitext(os.path.basename(self.last_generated_file))[0]
            export_base_folder = os.path.join(self.output_folder, f"SOMACA_Images_{base_name}")

            barcode_folder = None
            qr_folder = None

            if export_barcodes:
                barcode_folder = os.path.join(export_base_folder, "Codes-Barres")
                os.makedirs(barcode_folder, exist_ok=True)

            if export_qr:
                qr_folder = os.path.join(export_base_folder, "QR-Codes-Avec-Texte")
                os.makedirs(qr_folder, exist_ok=True)

            # Lire les données originales du fichier d'entrée pour générer les images avec texte
            data, original_headers = self.read_excel_data_like_old_app(self.input_file)
            if not data:
                return {"success": False, "message": "Impossible de lire les données originales"}

            exported_count = 0
            barcode_count = 0
            qr_count = 0
            total_items = len(data)

            print(f"📊 Génération de {total_items} images avec texte descriptif...")

            for i, row_data in enumerate(data):
                try:
                    # Générer le code-barres avec texte (si demandé)
                    if export_barcodes and barcode_folder:
                        barcode_count += 1

                        # Données pour le code-barres : 2 premières colonnes
                        col1 = row_data[0] if len(row_data) > 0 else ""
                        col2 = row_data[1] if len(row_data) > 1 else ""
                        barcode_data = f"{col1}-{col2}"

                        # Texte descriptif pour le code-barres (2 premières colonnes)
                        barcode_text_parts = []
                        for j in range(min(2, len(row_data), len(original_headers))):
                            if row_data[j] and original_headers[j]:
                                barcode_text_parts.append(f"{original_headers[j]}: {row_data[j]}")
                        barcode_text = "\n".join(barcode_text_parts)

                        filename = f"Barcode_avec_texte_{barcode_count:03d}.png"
                        save_path = os.path.join(barcode_folder, filename)

                        # Générer le code-barres avec texte
                        self.generate_high_res_barcode_with_text(barcode_data, barcode_text, save_path)
                        print(f"📏 Code-barres avec texte généré: {filename}")

                    # Générer le QR code avec texte (si demandé)
                    if export_qr and qr_folder:
                        qr_count += 1

                        # Données pour le QR code : TOUTES les informations
                        qr_data_parts = []
                        for j, value in enumerate(row_data):
                            if value and j < len(original_headers):
                                header_name = original_headers[j]
                                qr_data_parts.append(f"{header_name}:{value}")
                        qr_data = "|".join(qr_data_parts)

                        # Texte descriptif pour le QR code (2 premières colonnes)
                        qr_text_parts = []
                        for j in range(min(2, len(row_data), len(original_headers))):
                            if row_data[j] and original_headers[j]:
                                qr_text_parts.append(f"{original_headers[j]}: {row_data[j]}")
                        qr_text = "\n".join(qr_text_parts)

                        filename = f"QR_avec_texte_{qr_count:03d}.png"
                        save_path = os.path.join(qr_folder, filename)

                        # Générer le QR code avec texte
                        self.generate_high_res_qr_code_with_text(qr_data, qr_text, save_path)
                        print(f"📱 QR code avec texte généré: {filename}")

                    exported_count += 1

                    # Mise à jour progression
                    progress = (i + 1) / total_items * 100
                    try:
                        eel.update_progress(int(progress))
                    except:
                        pass

                except Exception as img_error:
                    print(f"⚠️ Erreur génération image ligne {i+1}: {img_error}")
                    continue

            print(f"✅ Extraction terminée: {barcode_count} codes-barres + {qr_count} QR codes")

            return {
                "success": True,
                "message": f"Extraction terminée ! {barcode_count} codes-barres et {qr_count} QR codes extraits.\nDossier: {export_base_folder}",
                "exported": exported_count,
                "barcodes": barcode_count,
                "qr_codes": qr_count,
                "folder": export_base_folder
            }

        except Exception as e:
            print(f"❌ Erreur lors de l'extraction: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "message": f"Erreur lors de l'extraction: {str(e)}"}

    def generate_all_codes_parallel(self, data, original_headers, generate_barcodes, generate_qr):
        """Génère TOUS les codes en parallèle pour une vitesse maximale"""
        def generate_codes_for_row(row_data):
            # Code-barres
            barcode_img = None
            if generate_barcodes:
                col1 = row_data[0] if len(row_data) > 0 else ""
                col2 = row_data[1] if len(row_data) > 1 else ""
                barcode_data = f"{col1}-{col2}"
                barcode_img = self.generate_barcode_image(barcode_data)

            # QR Code
            qr_img = None
            if generate_qr:
                qr_data_parts = []
                for i, value in enumerate(row_data):
                    if value and i < len(original_headers):
                        header_name = original_headers[i]
                        qr_data_parts.append(f"{header_name}:{value}")
                qr_data = "|".join(qr_data_parts)
                qr_img = self.generate_qr_code_image(qr_data)

            return barcode_img, qr_img

        # Génération parallèle avec ThreadPoolExecutor
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            results = list(executor.map(generate_codes_for_row, data))

        # Séparer les résultats
        barcodes = [result[0] for result in results]
        qr_codes = [result[1] for result in results]

        return {'barcodes': barcodes, 'qr_codes': qr_codes}

    def add_enhanced_data_row_fast(self, worksheet, row_num, row_data, original_headers, barcode_img, qr_img, generate_barcodes, generate_qr):
        """Version ULTRA-RAPIDE d'ajout de ligne avec images pré-générées"""
        num_original_cols = len(original_headers)

        # Réutiliser les styles (créés une seule fois)
        if not hasattr(self, '_data_font'):
            self._data_font = Font(size=10)
            self._data_alignment = Alignment(horizontal="center", vertical="center")

        data_font = self._data_font
        data_alignment = self._data_alignment

        # Couleur alternée pour les lignes
        if row_num % 2 == 0:
            fill_color = PatternFill(start_color="F8F9FA", end_color="F8F9FA", fill_type="solid")
        else:
            fill_color = PatternFill(start_color="FFFFFF", end_color="FFFFFF", fill_type="solid")

        # Ajouter toutes les données originales RAPIDEMENT
        for col, data in enumerate(row_data, 1):
            cell = worksheet.cell(row=row_num, column=col, value=data)
            cell.font = data_font
            cell.alignment = data_alignment
            cell.fill = fill_color

        # Calculer les positions des colonnes pour les codes
        barcode_col = None
        qr_col = None

        next_col = num_original_cols + 1
        if generate_barcodes:
            barcode_col = next_col
            next_col += 1
        if generate_qr:
            qr_col = next_col

        # Ajouter les images pré-générées RAPIDEMENT
        if generate_barcodes and barcode_col and barcode_img:
            barcode_cell = worksheet.cell(row=row_num, column=barcode_col, value="")
            barcode_cell.font = data_font
            barcode_cell.alignment = data_alignment
            barcode_cell.fill = fill_color

            barcode_col_letter = chr(ord('A') + barcode_col - 1)
            barcode_img.anchor = f"{barcode_col_letter}{row_num}"
            worksheet.add_image(barcode_img)

        if generate_qr and qr_col and qr_img:
            qr_cell = worksheet.cell(row=row_num, column=qr_col, value="")
            qr_cell.font = data_font
            qr_cell.alignment = data_alignment
            qr_cell.fill = fill_color

            qr_col_letter = chr(ord('A') + qr_col - 1)
            qr_img.anchor = f"{qr_col_letter}{row_num}"
            worksheet.add_image(qr_img)

        # Ajuster la hauteur de la ligne
        if generate_barcodes and generate_qr:
            worksheet.row_dimensions[row_num].height = 45
        elif generate_qr:
            worksheet.row_dimensions[row_num].height = 45
        elif generate_barcodes:
            worksheet.row_dimensions[row_num].height = 35
        else:
            worksheet.row_dimensions[row_num].height = 25





# Instance globale
generator = SomacaBarcodeGenerator()

# Fonctions exposées
@eel.expose
def set_input_file(file_path):
    """Définir le fichier d'entrée"""
    return generator.set_input_file(file_path)

@eel.expose
def set_output_folder(folder_path):
    """Définir le dossier de sortie"""
    return generator.set_output_folder(folder_path)

@eel.expose
def get_progress():
    """Obtenir le progrès actuel"""
    return generator.get_progress()

@eel.expose
def generate_codes(generate_barcodes=True, generate_qr=True):
    """Générer les codes-barres et QR codes"""
    return generator.generate_codes(generate_barcodes, generate_qr)

@eel.expose
def select_file():
    """Ouvrir le sélecteur de fichier"""
    try:
        import tkinter as tk
        from tkinter import filedialog
        import threading

        result = {"file_path": None, "error": None}

        def open_dialog():
            try:
                root = tk.Tk()
                root.withdraw()  # Cacher la fenêtre principale
                root.attributes('-topmost', True)  # Mettre au premier plan

                file_path = filedialog.askopenfilename(
                    title="Sélectionner un fichier Excel",
                    filetypes=[("Fichiers Excel", "*.xlsx *.xls"), ("Tous les fichiers", "*.*")],
                    parent=root
                )

                result["file_path"] = file_path
                root.destroy()

            except Exception as e:
                result["error"] = str(e)

        # Lancer dans un thread séparé
        thread = threading.Thread(target=open_dialog)
        thread.daemon = True
        thread.start()
        thread.join(timeout=30)  # Attendre max 30 secondes

        if result["error"]:
            return {"success": False, "message": f"Erreur: {result['error']}"}

        if result["file_path"]:
            return generator.set_input_file(result["file_path"])
        else:
            return {"success": False, "message": "Aucun fichier sélectionné"}

    except Exception as e:
        return {"success": False, "message": f"Erreur: {str(e)}"}

@eel.expose
def select_folder():
    """Ouvrir le sélecteur de dossier"""
    try:
        import tkinter as tk
        from tkinter import filedialog
        import threading

        result = {"folder_path": None, "error": None}

        def open_dialog():
            try:
                root = tk.Tk()
                root.withdraw()  # Cacher la fenêtre principale
                root.attributes('-topmost', True)  # Mettre au premier plan

                folder_path = filedialog.askdirectory(
                    title="Sélectionner un dossier de destination",
                    parent=root
                )

                result["folder_path"] = folder_path
                root.destroy()

            except Exception as e:
                result["error"] = str(e)

        # Lancer dans un thread séparé
        thread = threading.Thread(target=open_dialog)
        thread.daemon = True
        thread.start()
        thread.join(timeout=30)  # Attendre max 30 secondes

        if result["error"]:
            return {"success": False, "message": f"Erreur: {result['error']}"}

        if result["folder_path"]:
            return generator.set_output_folder(result["folder_path"])
        else:
            return {"success": False, "message": "Aucun dossier sélectionné"}

    except Exception as e:
        return {"success": False, "message": f"Erreur: {str(e)}"}

@eel.expose
def start_generation(generate_barcodes, generate_qr):
    """Démarrer la génération dans un thread séparé pour éviter le blocage"""
    import threading

    def run_generation():
        try:
            # Réinitialiser l'état d'annulation
            generator.is_cancelled = False

            print(f"Démarrage génération - Codes-barres: {generate_barcodes}, QR: {generate_qr}")
            print(f"Fichier d'entrée: {generator.input_file}")
            print(f"Dossier de sortie: {generator.output_folder}")

            result = generate_codes(generate_barcodes, generate_qr)
            print(f"Résultat: {result}")

            # Vérifier si l'opération a été annulée
            if generator.is_cancelled:
                result = {"success": False, "message": "Opération annulée par l'utilisateur"}

            # Notifier la fin de génération
            try:
                print(f"📞 Appel de generation_complete avec: {result}")
                eel.generation_complete(result)
                print("✅ generation_complete appelée avec succès")
            except Exception as e:
                print(f"❌ Erreur lors de l'appel de generation_complete: {e}")
                pass

        except Exception as e:
            print(f"❌ Erreur dans le thread de génération: {e}")
            try:
                eel.generation_complete({"success": False, "message": f"Erreur: {str(e)}"})
            except:
                pass

    # Lancer dans un thread séparé
    thread = threading.Thread(target=run_generation)
    thread.daemon = True
    thread.start()

    # Stocker la référence du thread
    generator.current_thread = thread

    return {"success": True, "message": "Génération démarrée..."}

@eel.expose
def export_images_for_print(export_barcodes=True, export_qr=True):
    """Exporter les images haute résolution pour impression selon les choix utilisateur"""
    return generator.export_images_for_print(export_barcodes, export_qr)

@eel.expose
def cancel_current_operation():
    """Annuler l'opération en cours"""
    return generator.cancel_operation()

@eel.expose
def print_directly(export_barcodes=True, export_qr=True):
    """Imprimer directement sur l'imprimante avec disposition optimale A4"""
    try:
        if not hasattr(generator, 'last_generated_file') or not generator.last_generated_file:
            return {"success": False, "message": "Aucun fichier Excel généré à imprimer"}

        if not os.path.exists(generator.last_generated_file):
            return {"success": False, "message": "Le fichier Excel généré n'existe plus"}

        print(f"Impression directe depuis: {generator.last_generated_file}")

        # Vérifier les imprimantes disponibles
        try:
            import win32print
            printer_info = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS)
            if not printer_info:
                return {"success": False, "message": "Aucune imprimante détectée sur le système"}

            default_printer = win32print.GetDefaultPrinter()
            print(f"Imprimante par défaut: {default_printer}")
        except Exception as e:
            return {"success": False, "message": f"Erreur détection imprimantes: {str(e)}"}

        # Extraire les images du fichier Excel
        from openpyxl import load_workbook
        workbook = load_workbook(generator.last_generated_file)
        worksheet = workbook.active

        barcode_images = []
        qr_images = []

        for image in worksheet._images:
            try:
                image_data = image._data()
                pil_img = Image.open(io.BytesIO(image_data))
                width, height = pil_img.size

                # Redimensionner à la résolution d'impression (300 DPI)
                if width > height:  # Code-barres
                    if export_barcodes:
                        # 3.5cm x 1.5cm à 300 DPI = 413x177 pixels
                        resized_img = pil_img.resize((413, 177), Image.Resampling.LANCZOS)
                        barcode_images.append(resized_img)
                else:  # QR code (carré)
                    if export_qr:
                        # 2cm x 2cm à 300 DPI = 236x236 pixels
                        resized_img = pil_img.resize((236, 236), Image.Resampling.LANCZOS)
                        qr_images.append(resized_img)

            except Exception as img_error:
                print(f"Erreur traitement image: {img_error}")
                continue

        workbook.close()

        if not barcode_images and not qr_images:
            return {"success": False, "message": "Aucune image à imprimer trouvée"}

        print(f"Images à imprimer: {len(barcode_images)} codes-barres, {len(qr_images)} QR codes")

        # Créer une disposition optimale A4 pour l'impression
        return create_print_layout(barcode_images, qr_images, default_printer)

    except Exception as e:
        print(f"Erreur impression directe: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "message": f"Erreur impression: {str(e)}"}

def create_print_layout(barcode_images, qr_images, printer_name):
    """Créer une disposition optimale A4 pour l'impression"""
    try:
        from PIL import Image, ImageDraw
        import tempfile
        import os

        # Dimensions A4 à 300 DPI
        a4_width = 2480  # 21cm à 300 DPI
        a4_height = 3508  # 29.7cm à 300 DPI
        margin = 150  # 1.3cm de marge
        spacing = 30  # 0.25cm d'espacement

        usable_width = a4_width - 2 * margin
        usable_height = a4_height - 2 * margin

        # Dimensions des images
        barcode_width, barcode_height = 413, 177  # 3.5cm x 1.5cm
        qr_width, qr_height = 236, 236  # 2cm x 2cm

        # Calculer le nombre de colonnes et lignes pour les codes-barres
        barcode_cols = max(1, (usable_width + spacing) // (barcode_width + spacing))
        barcode_rows = max(1, (usable_height + spacing) // (barcode_height + spacing))
        barcode_per_page = barcode_cols * barcode_rows

        # Calculer le nombre de colonnes et lignes pour les QR codes
        qr_cols = max(1, (usable_width + spacing) // (qr_width + spacing))
        qr_rows = max(1, (usable_height + spacing) // (qr_height + spacing))
        qr_per_page = qr_cols * qr_rows

        print(f"Disposition A4: {barcode_cols}x{barcode_rows} codes-barres, {qr_cols}x{qr_rows} QR codes par page")

        temp_folder = tempfile.mkdtemp()
        pdf_files = []

        # Créer les pages pour les codes-barres
        if barcode_images:
            for page in range(0, len(barcode_images), barcode_per_page):
                page_img = Image.new('RGB', (a4_width, a4_height), 'white')
                page_barcodes = barcode_images[page:page + barcode_per_page]

                for i, barcode in enumerate(page_barcodes):
                    row = i // barcode_cols
                    col = i % barcode_cols
                    x = margin + col * (barcode_width + spacing)
                    y = margin + row * (barcode_height + spacing)
                    page_img.paste(barcode, (x, y))

                pdf_path = os.path.join(temp_folder, f"codes_barres_page_{page//barcode_per_page + 1}.pdf")
                page_img.save(pdf_path, 'PDF', resolution=300.0)
                pdf_files.append(pdf_path)

        # Créer les pages pour les QR codes
        if qr_images:
            for page in range(0, len(qr_images), qr_per_page):
                page_img = Image.new('RGB', (a4_width, a4_height), 'white')
                page_qrs = qr_images[page:page + qr_per_page]

                for i, qr in enumerate(page_qrs):
                    row = i // qr_cols
                    col = i % qr_cols
                    x = margin + col * (qr_width + spacing)
                    y = margin + row * (qr_height + spacing)
                    page_img.paste(qr, (x, y))

                pdf_path = os.path.join(temp_folder, f"qr_codes_page_{page//qr_per_page + 1}.pdf")
                page_img.save(pdf_path, 'PDF', resolution=300.0)
                pdf_files.append(pdf_path)

        # Ouvrir les fichiers PDF pour impression
        if pdf_files:
            for pdf_file in pdf_files:
                os.startfile(pdf_file)

            return {
                "success": True,
                "message": f"Fichiers PDF créés pour impression !\n{len(pdf_files)} pages générées\nOuverts pour impression sur {printer_name}",
                "files_created": len(pdf_files),
                "temp_folder": temp_folder,
                "barcode_pages": len([f for f in pdf_files if "codes_barres" in f]),
                "qr_pages": len([f for f in pdf_files if "qr_codes" in f])
            }
        else:
            return {"success": False, "message": "Aucun fichier PDF créé"}

    except Exception as e:
        print(f"Erreur création disposition: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "message": f"Erreur création disposition: {str(e)}"}

@eel.expose
def user_file_choice(choice):
    """Recevoir le choix de l'utilisateur pour le fichier existant"""
    generator._user_choice = choice
    return {"success": True}

@eel.expose
def generate_renault_etiquettes():
    """Générer les étiquettes Renault pour toutes les lignes"""
    try:
        if not generator.input_file or not os.path.exists(generator.input_file):
            return {"success": False, "message": "Aucun fichier d'entrée sélectionné"}

        # Lire les données
        data, headers = generator.read_excel_data()
        if not data:
            return {"success": False, "message": "Aucune donnée trouvée dans le fichier"}

        # Créer le dossier de sortie pour les étiquettes
        etiquettes_folder = os.path.join(generator.output_folder, "etiquettes_renault")
        os.makedirs(etiquettes_folder, exist_ok=True)

        success_count = 0
        error_count = 0

        for i, row in enumerate(data):
            try:
                # Créer étiquette code-barres
                barcode_html = generator.create_renault_etiquette(row, "barcode")
                if barcode_html:
                    barcode_filename = os.path.join(etiquettes_folder, f"barcode_{i+1}_{row[0]}.html".replace('/', '_').replace('-', '_'))
                    with open(barcode_filename, 'w', encoding='utf-8') as f:
                        f.write(barcode_html)

                # Créer étiquette QR code
                qr_html = generator.create_renault_etiquette(row, "qr")
                if qr_html:
                    qr_filename = os.path.join(etiquettes_folder, f"qr_{i+1}_{row[0]}.html".replace('/', '_').replace('-', '_'))
                    with open(qr_filename, 'w', encoding='utf-8') as f:
                        f.write(qr_html)

                success_count += 1

            except Exception as e:
                print(f"Erreur ligne {i+1}: {e}")
                error_count += 1

        return {
            "success": True,
            "message": f"Étiquettes Renault générées !\n{success_count} lignes traitées\n{error_count} erreurs\nDossier: {etiquettes_folder}",
            "folder": etiquettes_folder,
            "success_count": success_count,
            "error_count": error_count
        }

    except Exception as e:
        print(f"Erreur génération étiquettes Renault: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "message": f"Erreur: {str(e)}"}

@eel.expose
def open_folder(folder_path):
    """Ouvrir un dossier dans l'explorateur"""
    try:
        import subprocess
        import platform

        if platform.system() == "Windows":
            subprocess.run(["explorer", folder_path])
        elif platform.system() == "Darwin":  # macOS
            subprocess.run(["open", folder_path])
        else:  # Linux
            subprocess.run(["xdg-open", folder_path])

        return {"success": True}
    except Exception as e:
        print(f"Erreur ouverture dossier: {e}")
        return {"success": False, "message": str(e)}

@eel.expose
def debug_info():
    """Informations de debug"""
    return {
        "input_file": generator.input_file,
        "output_folder": generator.output_folder,
        "input_exists": os.path.exists(generator.input_file) if generator.input_file else False,
        "output_exists": os.path.exists(generator.output_folder) if generator.output_folder else False,
        "current_dir": os.getcwd()
    }

if __name__ == '__main__':
    # Démarrer l'application avec l'icône SOMACA
    eel.start('index.html',
              size=(1000, 700),
              port=8081,
              mode='chrome-app',
              cmdline_args=['--app-name=SOMACA • Générateur de Codes-Barres'])
