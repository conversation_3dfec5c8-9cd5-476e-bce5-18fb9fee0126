
@echo off
echo ========================================
echo    SOMACA - Generateur de Codes-Barres
echo    Version NATIVE (PyWebView)
echo    Installation automatique
echo ========================================
echo.

echo Installation des dependances Python...
pip install pywebview pandas openpyxl qrcode python-barcode Pillow pyinstaller

echo.
echo Creation de l'executable NATIVE...
python build_native.py

echo.
echo ========================================
echo Installation terminee !
echo L'executable NATIF se trouve dans le dossier 'dist/'
echo Nom: SOMACA_Native.exe
echo ========================================
echo.
echo AVANTAGES de cette version:
echo - Application 100%% native Windows
echo - Aucune dependance externe
echo - Interface identique a la version web
echo - Fonctionne sur tous les Windows
echo - Utilise Edge WebView2 integre
echo ========================================
pause
