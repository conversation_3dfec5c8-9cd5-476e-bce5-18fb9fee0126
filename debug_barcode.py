#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug des codes-barres - Test direct
"""

import barcode
from barcode.writer import ImageWriter
import io
from openpyxl import Workbook
from openpyxl.drawing.image import Image as OpenpyxlImage

def clean_text_for_barcode(text):
    """Nettoyer le texte pour le rendre compatible avec Code128"""
    if not text:
        return ""
    
    # Convertir en string si ce n'est pas déjà le cas
    text = str(text)

    # Remplacer les caractères accentués par leurs équivalents ASCII
    replacements = {
        'à': 'a', 'á': 'a', 'â': 'a', 'ã': 'a', 'ä': 'a', 'å': 'a',
        'è': 'e', 'é': 'e', 'ê': 'e', 'ë': 'e',
        'ì': 'i', 'í': 'i', 'î': 'i', 'ï': 'i',
        'ò': 'o', 'ó': 'o', 'ô': 'o', 'õ': 'o', 'ö': 'o',
        'ù': 'u', 'ú': 'u', 'û': 'u', 'ü': 'u',
        'ý': 'y', 'ÿ': 'y',
        'ç': 'c', 'ñ': 'n',
        'À': 'A', 'Á': 'A', 'Â': 'A', 'Ã': 'A', 'Ä': 'A', 'Å': 'A',
        'È': 'E', 'É': 'E', 'Ê': 'E', 'Ë': 'E',
        'Ì': 'I', 'Í': 'I', 'Î': 'I', 'Ï': 'I',
        'Ò': 'O', 'Ó': 'O', 'Ô': 'O', 'Õ': 'O', 'Ö': 'O',
        'Ù': 'U', 'Ú': 'U', 'Û': 'U', 'Ü': 'U',
        'Ý': 'Y', 'Ÿ': 'Y',
        'Ç': 'C', 'Ñ': 'N'
    }

    cleaned_text = str(text)
    for accented, ascii_char in replacements.items():
        cleaned_text = cleaned_text.replace(accented, ascii_char)

    # Supprimer tous les caractères non-ASCII restants
    cleaned_text = ''.join(char for char in cleaned_text if ord(char) < 128)

    return cleaned_text

def is_ascii_compatible(text):
    """Vérifier si le texte ne contient que des caractères compatibles avec Code128"""
    try:
        # Code128 supporte les caractères ASCII de 0 à 127
        text.encode('ascii')
        return True
    except UnicodeEncodeError:
        return False

def generate_barcode_image(data):
    """Génère une image de code-barre OPTIMISÉE avec dimensions exactes : 3,5cm x 1,5cm"""
    try:
        print(f"🔍 Test génération code-barre pour: '{data}'")
        
        # NETTOYER D'ABORD les données pour les rendre compatibles
        cleaned_data = clean_text_for_barcode(str(data))
        print(f"🧹 Données nettoyées: '{cleaned_data}'")
        
        # Vérifier si les données nettoyées sont compatibles
        if not is_ascii_compatible(cleaned_data):
            print(f"❌ Ligne ignorée pour code-barre (caractères non supportés après nettoyage): {data}")
            return None

        print(f"✅ Données compatibles ASCII")

        # Obtenir la classe code128
        code128_class = barcode.get_barcode_class('code128')
        print(f"✅ Classe Code128 obtenue: {code128_class}")

        barcode_instance = code128_class(cleaned_data, writer=ImageWriter())
        print(f"✅ Instance Code128 créée")

        # Générer l'image en mémoire
        buffer = io.BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)
        print(f"✅ Image générée, taille: {len(buffer.getvalue())} bytes")

        # Créer l'objet image pour Excel
        img = OpenpyxlImage(buffer)
        print(f"✅ Image OpenpyXL créée: {img}")

        # Dimensions optimisées pour s'adapter parfaitement à la colonne
        img.width = 85   # 3cm - ajusté pour la nouvelle largeur de colonne
        img.height = 35  # 1.2cm - ajusté pour la hauteur de ligne

        print(f"✅ Code-barre généré avec succès !")
        return img
    except Exception as e:
        print(f"❌ Erreur génération code-barre: {data} - {e}")
        import traceback
        traceback.print_exc()
        return None

def test_barcode_generation():
    """Test complet de génération de codes-barres"""
    print("🚀 DÉBUT TEST GÉNÉRATION CODES-BARRES")
    print("=" * 50)
    
    # Tests avec différents types de données
    test_cases = [
        "TEST-123",
        "PRD001-Telephone",
        "PRD005-Montre Connectée Apple Watch",
        "SKU123-Produit Français",
        "ABC-éèçàù",
        ""
    ]
    
    workbook = Workbook()
    worksheet = workbook.active
    
    success_count = 0
    
    for i, test_data in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: '{test_data}'")
        print("-" * 30)
        
        barcode_img = generate_barcode_image(test_data)
        
        if barcode_img:
            print(f"✅ SUCCÈS - Code-barre généré")
            # Ajouter à Excel
            barcode_img.anchor = f"A{i}"
            worksheet.add_image(barcode_img)
            success_count += 1
        else:
            print(f"❌ ÉCHEC - Pas de code-barre généré")
            # Mettre un texte d'erreur
            worksheet.cell(row=i, column=1, value=f"ERREUR: {test_data}")
    
    # Sauvegarder le fichier de test
    test_file = "debug_barcode_test.xlsx"
    workbook.save(test_file)
    
    print("\n" + "=" * 50)
    print(f"🎯 RÉSULTAT FINAL:")
    print(f"✅ Succès: {success_count}/{len(test_cases)}")
    print(f"📁 Fichier test sauvé: {test_file}")
    
    return success_count > 0

if __name__ == "__main__":
    test_barcode_generation()
