@echo off
echo ========================================
echo    SOMACA - Générateur QR ^& Code à Barre
echo    Version MODIFIÉE avec améliorations
echo    Développé par: IMAD ELberrouagui
echo ========================================
echo.

echo 🎯 NOUVELLES FONCTIONNALITÉS:
echo   ✅ Texte SOMACA agrandi (50pt → 70pt)
echo   ✅ Position SOMACA à 0.7cm du haut
echo   ✅ Sous-titre "Société Marocaine..." SUPPRIMÉ
echo   ✅ QR code agrandi (2.5cm → 4.0cm)
echo   ✅ Espace 0.8cm entre SOMACA et QR code
echo   ✅ QR code centré horizontalement
echo.

echo 🚀 Lancement de l'application...
cd /d "%~dp0"

REM Essayer d'abord l'exécutable compilé
if exist "dist\SOMACA_Native_Fixed.exe" (
    echo ✅ Utilisation de l'exécutable compilé
    start "" "dist\SOMACA_Native_Fixed.exe"
    echo ✅ Application lancée !
    goto :end
)

if exist "dist\SOMACA_Native.exe" (
    echo ✅ Utilisation de l'exécutable (version ancienne)
    start "" "dist\SOMACA_Native.exe"
    echo ✅ Application lancée !
    goto :end
)

REM Si pas d'exécutable, utiliser Python global
echo 💡 Aucun exécutable trouvé, utilisation de Python...
python -c "import sys; print('Python version:', sys.version)" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python n'est pas installé ou accessible
    echo 💡 Installez Python depuis https://python.org
    pause
    goto :end
)

echo 🔧 Vérification des dépendances Python...
python -c "import webview, pandas, openpyxl, qrcode, barcode; print('✅ Dépendances OK')" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ Installation des dépendances manquantes...
    python -m pip install pywebview pandas openpyxl qrcode python-barcode Pillow
)

echo ✅ Lancement avec Python...
python app_native.py

:end
echo.
echo 💡 L'application s'ouvre dans une nouvelle fenêtre
echo 💡 Vous pouvez fermer cette console
echo.
pause
