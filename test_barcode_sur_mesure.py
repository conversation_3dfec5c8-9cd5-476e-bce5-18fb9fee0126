#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test code-barres sur mesure - barres exactes sans espaces
"""

import os
import base64

def get_logo_base64():
    """Convertit le logo Renault en base64"""
    try:
        logo_paths = ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"]
        for logo_path in logo_paths:
            if os.path.exists(logo_path):
                with open(logo_path, "rb") as f:
                    logo_data = f.read()
                print(f"✅ Logo trouvé: {logo_path}")
                return base64.b64encode(logo_data).decode('utf-8')
        return ""
    except Exception as e:
        print(f"⚠️ Erreur: {e}")
        return ""

def generate_barcode_svg(code_value):
    """Génère un code-barres SVG sur mesure avec barres exactes"""
    # Pattern simple pour code-barres (simulation Code 128)
    # Chaque caractère a un pattern de barres
    patterns = {
        '0': '11010010000', '1': '10010110000', '2': '10010001100', '3': '10001001100',
        '4': '10011001000', '5': '10000110100', '6': '10000001100', '7': '10010001000',
        '8': '10001000100', '9': '10001001000', '-': '10000100100'
    }
    
    # Créer le pattern complet
    full_pattern = '11010000100'  # Start pattern
    for char in code_value:
        if char in patterns:
            full_pattern += patterns[char]
    full_pattern += '1100011101011'  # Stop pattern
    
    # Générer les barres SVG
    bars = []
    x = 0
    bar_width = 2  # Largeur de chaque barre
    
    for bit in full_pattern:
        if bit == '1':  # Barre noire
            bars.append(f'<rect x="{x}" y="0" width="{bar_width}" height="100%" fill="black"/>')
        x += bar_width
    
    total_width = x
    
    # SVG du code-barres
    svg_content = f'''
    <svg width="100%" height="100%" viewBox="0 0 {total_width} 100" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="white"/>
        {''.join(bars)}
    </svg>
    '''
    
    return svg_content

def create_etiquette_sur_mesure(code_value="64004380-0", part_number="1/2"):
    """Créer une étiquette avec code-barres sur mesure"""
    
    logo_base64 = get_logo_base64()
    barcode_svg = generate_barcode_svg(code_value)
    
    html_content = f"""<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Étiquette Renault - Code-barres Sur Mesure</title>
  <style>
    body {{
      margin: 0;
      padding: 20px;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
    }}
    .label {{
      width: 6cm;
      height: 3cm;
      background-color: #000;
      border-radius: 8px;
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      font-family: Arial, sans-serif;
      margin: 0 auto;
      box-sizing: border-box;
      overflow: hidden;
      padding: 4px;
    }}
    .logo img {{
      height: 50px;
    }}
    .code-number {{
      font-size: 14px;
      font-weight: bold;
    }}
    .barcode-container {{
      /* Zone blanche exacte : 3cm x 0.8cm */
      width: 3cm;
      height: 0.8cm;
      background: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      box-sizing: border-box;
      padding: 0;
      margin: 0;
    }}
    .barcode-svg {{
      width: 100%;
      height: 100%;
      display: block;
    }}
  </style>
</head>
<body>
  <div id="labels"></div>
  <script>
    const label = document.createElement("div");
    label.className = "label";
    label.innerHTML = `
      <div class="logo"><img src="data:image/jpeg;base64,{logo_base64}" alt="Renault"></div>
      <div class="barcode-container">
        {barcode_svg}
      </div>
      <div class="code-number">{code_value} {part_number}</div>
    `;
    document.getElementById("labels").appendChild(label);
  </script>
</body>
</html>"""
    
    filename = f"etiquette_sur_mesure_{code_value.replace('-', '_')}_{part_number.replace('/', '_')}.html"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Étiquette sur mesure créée: {filename}")
    return filename

def main():
    """Test code-barres sur mesure"""
    print("🎯 Test Code-barres Sur Mesure (barres exactes)")
    print("=" * 50)
    
    # Vérifier qu'un logo existe
    logo_found = any(os.path.exists(path) for path in ["renault-logo.png", "renault-logo.jpg", "renault-logo.jpeg"])
    if not logo_found:
        print("❌ Logo Renault manquant!")
        return
    
    # Créer l'étiquette
    filename = create_etiquette_sur_mesure("64004380-0", "1/2")
    
    if filename:
        print(f"📁 Fichier créé: {filename}")
        print("💡 Code-barres généré avec barres exactes (pas de police)")
        print("✅ Les barres remplissent exactement la zone blanche")
        
        # Ouvrir automatiquement
        try:
            os.system(f"start {filename}")
        except:
            print("   (Ouvrez manuellement le fichier)")

if __name__ == "__main__":
    main()
