#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour vérifier la logique du bouton Renault
"""

import os
import sys

def test_button_logic():
    """Test de la logique du bouton Renault"""
    print("🧪 Test de la logique du bouton Renault")
    print("=" * 50)
    
    # Vérifier les fichiers modifiés
    files_to_check = [
        "web/script.js",
        "web/script_native.js", 
        "web/index.html"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path} existe")
            
            # Vérifier le contenu
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if file_path == "web/index.html":
                if 'btn-renault" onclick="generateRenaultEtiquettes()" disabled>' in content:
                    print(f"  ✅ Bouton Renault initialement désactivé")
                else:
                    print(f"  ❌ Bouton Renault pas désactivé au démarrage")
                    
            elif "script" in file_path:
                if "btnRenault.disabled = false" in content:
                    print(f"  ✅ Logique d'activation du bouton trouvée")
                else:
                    print(f"  ❌ Logique d'activation manquante")
                    
                if "resetExportButton" in content and "btnRenault" in content:
                    print(f"  ✅ Fonction de réinitialisation mise à jour")
                else:
                    print(f"  ❌ Fonction de réinitialisation pas mise à jour")
        else:
            print(f"❌ {file_path} n'existe pas")
    
    print("\n📋 Résumé des modifications:")
    print("1. ✅ Bouton Renault désactivé au démarrage (HTML)")
    print("2. ✅ Bouton activé après génération réussie (JS)")
    print("3. ✅ Bouton désactivé lors de la sélection d'un nouveau fichier (JS)")
    print("4. ✅ Bouton désactivé en cas d'erreur de génération (JS)")
    
    print("\n🎯 Comportement attendu:")
    print("- Au démarrage: Bouton Renault DÉSACTIVÉ")
    print("- Après sélection fichier: Bouton Renault DÉSACTIVÉ")
    print("- Après génération réussie: Bouton Renault ACTIVÉ")
    print("- Clic sur bouton Renault: Vérifie fichier Excel avec codes")
    print("- Si codes trouvés: Affiche modal de choix")
    print("- Génère étiquettes selon choix utilisateur")

if __name__ == "__main__":
    test_button_logic()
