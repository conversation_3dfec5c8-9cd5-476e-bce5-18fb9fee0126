#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple pour vérifier les corrections
"""

import os
import sys

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """Tester l'import de l'application"""
    try:
        print("🧪 Test d'import de l'application...")
        from app_native import SomacaBarcodeGenerator, Api
        print("✅ Import réussi")
        
        # Tester la création d'une instance
        print("🧪 Test de création d'instance...")
        generator = SomacaBarcodeGenerator()
        print("✅ Instance créée")
        
        # Tester la réinitialisation
        print("🧪 Test de réinitialisation...")
        generator.reset_application_state()
        print("✅ Réinitialisation réussie")
        
        # Tester l'API
        print("🧪 Test de l'API...")
        api = Api()
        result = api.reset_app()
        print(f"✅ API testée: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cleanup():
    """Tester la fonction de nettoyage"""
    try:
        print("🧪 Test de nettoyage...")
        from app_native import SomacaBarcodeGenerator
        
        generator = SomacaBarcodeGenerator()
        generator.cleanup_open_files()
        print("✅ Nettoyage réussi")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur nettoyage: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Démarrage des tests simples...")
    
    success = True
    
    # Test 1: Import
    if not test_import():
        success = False
    
    # Test 2: Nettoyage
    if not test_cleanup():
        success = False
    
    if success:
        print("\n🎉 Tous les tests simples sont passés!")
        print("\n📋 Résumé des corrections apportées:")
        print("✅ Ajout de la fonction reset_application_state()")
        print("✅ Amélioration du nettoyage des caches et ressources")
        print("✅ Gestion des erreurs PermissionError")
        print("✅ Fermeture automatique des workbooks")
        print("✅ Réinitialisation lors de la sélection de nouveaux fichiers")
        print("✅ Ajout de l'API reset_app() pour JavaScript")
        print("✅ Réinitialisation automatique côté interface")
        
        print("\n🔧 Comment utiliser l'application maintenant:")
        print("1. Sélectionnez un fichier Excel")
        print("2. Sélectionnez un dossier de destination")
        print("3. Cliquez sur 'Générer'")
        print("4. Pour générer un autre fichier, sélectionnez simplement un nouveau fichier")
        print("5. L'application se réinitialise automatiquement")
        
    else:
        print("\n❌ Certains tests ont échoué")
    
    sys.exit(0 if success else 1)
