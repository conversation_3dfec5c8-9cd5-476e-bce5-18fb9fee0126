#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour compiler l'application SOMACA en .exe avec l'icône personnalisée
Développé par: IMAD ELberrouagui
"""

import PyInstaller.__main__
import os
import sys

def build_exe():
    """Compiler l'application en .exe avec l'icône SOMACA"""
    
    # Chemin vers l'icône (priorité au .ico)
    icon_path = os.path.join(os.path.dirname(__file__), 'exel.ico')
    if not os.path.exists(icon_path):
        icon_path = os.path.join(os.path.dirname(__file__), 'exel.png')
    
    # Arguments pour PyInstaller
    args = [
        'app.py',                           # Script principal
        '--onefile',                        # Un seul fichier .exe
        '--windowed',                       # Pas de console
        '--name=SOMACA_Generateur_Codes',   # Nom de l'exécutable
        f'--icon={icon_path}',              # Icône de l'application
        '--add-data=web;web',               # Inclure le dossier web
        '--distpath=dist',                  # Dossier de sortie
        '--workpath=build',                 # Dossier de travail
        '--specpath=.',                     # Fichier .spec
        '--clean',                          # Nettoyer avant compilation
        '--noconfirm',                      # Pas de confirmation
    ]
    
    print("🚀 Compilation de l'application SOMACA en .exe...")
    print(f"📁 Icône utilisée: {icon_path}")
    print("⏳ Veuillez patienter...")
    
    try:
        # Lancer PyInstaller
        PyInstaller.__main__.run(args)
        
        print("✅ Compilation terminée avec succès !")
        print("📁 L'exécutable se trouve dans le dossier 'dist'")
        print("🎯 Nom du fichier: SOMACA_Generateur_Codes.exe")
        
        # Vérifier si le fichier existe
        exe_path = os.path.join('dist', 'SOMACA_Generateur_Codes.exe')
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📊 Taille du fichier: {size_mb:.1f} MB")
        
    except Exception as e:
        print(f"❌ Erreur lors de la compilation: {e}")
        return False
    
    return True

if __name__ == '__main__':
    print("=" * 60)
    print("🏭 SOMACA - Générateur de Codes-Barres")
    print("🔧 Compilation en exécutable Windows (.exe)")
    print("👨‍💻 Développé par: IMAD ELberrouagui")
    print("=" * 60)
    
    success = build_exe()
    
    if success:
        print("\n🎉 Application compilée avec succès !")
        print("💡 L'icône SOMACA sera visible dans la barre des tâches")
        print("🚀 Vous pouvez maintenant distribuer le fichier .exe")
    else:
        print("\n❌ Échec de la compilation")
        sys.exit(1)
